# Event Slider Plugin Build Scripts

This document explains how to use the build scripts to create an installable WordPress plugin zip file.

## Available Build Scripts

We provide multiple build scripts for different environments:

1. **`build.ps1`** - PowerShell script (Windows, Linux, macOS)
2. **`build.sh`** - Bash script (Linux, macOS, Windows with WSL)
3. **`build.js`** - Node.js script (Cross-platform)
4. **`build.bat`** - Batch script (Windows only)

## Quick Start

### Windows Users

**Option 1: Double-click (Easiest)**
```
Double-click build.bat
```

**Option 2: PowerShell (Recommended)**
```powershell
.\build.ps1
```

**Option 3: Command Prompt**
```cmd
build.bat
```

### Linux/Mac Users

**Option 1: Bash Script**
```bash
./build.sh
```

**Option 2: Node.js (if Node.js is installed)**
```bash
node build.js
```

### Cross-Platform (Node.js)

If you have Node.js installed:
```bash
node build.js
```

## Script Options

All scripts support the following options:

### PowerShell Script (`build.ps1`)
```powershell
# Basic usage
.\build.ps1

# With custom version
.\build.ps1 -Version "1.2.0"

# Clean previous builds
.\build.ps1 -Clean

# Custom output directory
.\build.ps1 -OutputDir "releases"

# Combined options
.\build.ps1 -Version "1.2.0" -Clean -OutputDir "releases"
```

### Bash Script (`build.sh`)
```bash
# Basic usage
./build.sh

# With options
./build.sh --version 1.2.0 --clean --output releases

# Short options
./build.sh -v 1.2.0 -c -o releases

# Help
./build.sh --help
```

### Node.js Script (`build.js`)
```bash
# Basic usage
node build.js

# With options
node build.js --version 1.2.0 --clean --output releases

# Short options
node build.js -v 1.2.0 -c -o releases

# Help
node build.js --help
```

### Batch Script (`build.bat`)
```cmd
# Basic usage (no options supported)
build.bat
```

## What Gets Included

The build scripts automatically include:

### Files
- `event-slider.php` (main plugin file)
- `readme.txt` (WordPress plugin readme)

### Directories
- `includes/` (PHP classes and functionality)
- `assets/` (CSS, JavaScript, and other assets)

## What Gets Excluded

The build scripts automatically exclude development and unnecessary files:

### File Patterns
- `*.md` (Markdown documentation)
- `*.sh`, `*.ps1`, `*.bat` (Build scripts)
- `build.js` (Node.js build script)
- `package.json`, `composer.json` (Dependency files)
- `*.log`, `*.tmp` (Temporary files)
- `*.bak`, `*.orig` (Backup files)

### Directories
- `.git*` (Git files)
- `node_modules/` (Node.js dependencies)
- `vendor/` (Composer dependencies)
- `dist/` (Build output)
- `.vscode/`, `.idea/` (Editor settings)

### System Files
- `.DS_Store` (macOS)
- `Thumbs.db` (Windows)

## Output

The build process creates:

```
dist/
└── event-slider-v1.0.0.zip
```

The zip file contains:
```
event-slider/
├── event-slider.php
├── readme.txt
├── includes/
│   ├── class-event-slider.php
│   └── widgets/
│       └── class-event-slider-widget.php
└── assets/
    ├── css/
    │   └── event-slider.css
    └── js/
        ├── event-slider.js
        └── event-slider-editor.js
```

## Version Management

The build scripts automatically update version numbers in:

1. **Plugin Header** in `event-slider.php`:
   ```php
   * Version: 1.0.0
   ```

2. **Version Constant** in `event-slider.php`:
   ```php
   define('EVENT_SLIDER_VERSION', '1.0.0');
   ```

## Requirements

### PowerShell Script
- PowerShell 5.0+ (Windows)
- PowerShell Core 6.0+ (Linux/macOS)

### Bash Script
- Bash shell
- `zip` command
- `unzip` command (for listing contents)

**Install zip on Ubuntu/Debian:**
```bash
sudo apt-get install zip unzip
```

**Install zip on CentOS/RHEL:**
```bash
sudo yum install zip unzip
```

### Node.js Script
- Node.js 12.0+
- No additional dependencies required

### Batch Script
- Windows with PowerShell
- No additional software required

## Troubleshooting

### "zip command not found" (Linux/Mac)
Install the zip utility:
```bash
# Ubuntu/Debian
sudo apt-get install zip

# macOS (usually pre-installed)
# If missing, install via Homebrew:
brew install zip
```

### "PowerShell not found" (Windows)
PowerShell should be pre-installed on Windows 10+. For older versions:
1. Download from Microsoft's website
2. Or use the batch script instead

### Permission denied (Linux/Mac)
Make the script executable:
```bash
chmod +x build.sh
```

### Node.js not found
Install Node.js from [nodejs.org](https://nodejs.org/)

## Advanced Usage

### Custom Build Configuration

You can modify the build scripts to:

1. **Add more files to include:**
   ```bash
   # In build.sh, add to INCLUDE_FILES array
   INCLUDE_FILES=(
       "event-slider.php"
       "readme.txt"
       "LICENSE"  # Add this
   )
   ```

2. **Exclude additional patterns:**
   ```bash
   # In build.sh, add to EXCLUDE_PATTERNS array
   EXCLUDE_PATTERNS=(
       "*.md"
       "*.test.php"  # Add this
   )
   ```

3. **Change output naming:**
   ```bash
   # Modify ZIP_FILE variable
   ZIP_FILE="$BUILD_DIR/my-custom-name-v$VERSION.zip"
   ```

### Automated Builds

You can integrate these scripts into CI/CD pipelines:

**GitHub Actions Example:**
```yaml
- name: Build Plugin
  run: ./build.sh --version ${{ github.ref_name }} --clean
```

**Jenkins Example:**
```groovy
sh './build.sh --version ${BUILD_NUMBER} --clean'
```

## Best Practices

1. **Always test the built plugin** before distribution
2. **Use semantic versioning** (e.g., 1.0.0, 1.1.0, 2.0.0)
3. **Clean builds** when changing versions
4. **Verify zip contents** before uploading to WordPress
5. **Keep build scripts updated** with your project structure

## Support

If you encounter issues with the build scripts:

1. Check the requirements section
2. Verify file permissions
3. Ensure all required tools are installed
4. Check the console output for specific error messages

For script-specific issues, try an alternative build method (e.g., if PowerShell fails, try Node.js).
