# Event Slider Plugin Installation Guide

## Requirements

Before installing the Event Slider plugin, make sure your WordPress site meets these requirements:

- **WordPress**: 5.0 or higher
- **PHP**: 7.4 or higher
- **Elementor**: 3.0.0 or higher (Free or Pro)
- **MySQL**: 5.0 or higher

## Installation Methods

### Method 1: Upload via WordPress Admin (Recommended)

1. **Download the Plugin**
   - Download the `event-slider.zip` file

2. **Upload to WordPress**
   - Log in to your WordPress admin dashboard
   - Navigate to `Plugins > Add New`
   - Click `Upload Plugin`
   - Choose the `event-slider.zip` file
   - Click `Install Now`

3. **Activate the Plugin**
   - After installation, click `Activate Plugin`
   - The plugin will now be available in Elementor

### Method 2: FTP Upload

1. **Extract the Plugin**
   - Extract the `event-slider.zip` file to your computer

2. **Upload via FTP**
   - Connect to your website via FTP
   - Navigate to `/wp-content/plugins/`
   - Upload the entire `event-slider` folder

3. **Activate the Plugin**
   - Go to your WordPress admin dashboard
   - Navigate to `Plugins > Installed Plugins`
   - Find "Event Slider" and click `Activate`

### Method 3: Manual Installation

1. **Copy Plugin Files**
   - Copy all plugin files to your WordPress installation directory
   - Place them in `/wp-content/plugins/event-slider/`

2. **Set Permissions**
   - Ensure proper file permissions (644 for files, 755 for directories)

3. **Activate the Plugin**
   - Go to WordPress admin > Plugins
   - Activate the Event Slider plugin

## Post-Installation Setup

### 1. Verify Installation

After activation, check that:
- No error messages appear
- Elementor is still working properly
- The plugin appears in the active plugins list

### 2. Check Elementor Integration

1. Edit any page with Elementor
2. In the widgets panel, look for the "Event Slider" category
3. You should see the "Event Slider" widget available

### 3. Test the Widget

1. Drag the Event Slider widget to your page
2. Add some sample events
3. Preview the page to ensure everything works

## Using the Event Slider Widget

### Adding the Widget

1. **Open Elementor Editor**
   - Edit any page or post with Elementor

2. **Find the Widget**
   - In the widgets panel, search for "Event Slider"
   - Or find it under the "Event Slider" category

3. **Add to Page**
   - Drag and drop the widget to your desired location

### Configuring Events

1. **Add Event Slides**
   - Click on the widget to open settings
   - In the "Events" tab, click "Add Item" to add new events
   - For each event, configure:
     - **Image**: Upload or select an event image
     - **Title**: Enter the event title
     - **Subtitle**: Add event description
     - **Button Text**: Set the call-to-action text
     - **Button Link**: Add the destination URL

2. **Slider Settings**
   - Configure slider behavior:
     - **Slides to Show**: Number of slides visible at once
     - **Slides to Scroll**: Number of slides to move per navigation
     - **Autoplay**: Enable/disable automatic sliding
     - **Autoplay Speed**: Set timing for auto-advance
     - **Infinite Loop**: Enable continuous looping
     - **Show Arrows**: Display navigation arrows
     - **Show Dots**: Display dot indicators

### Styling Options

1. **Slide Style**
   - Customize slide appearance, padding, background, borders, and shadows

2. **Title Style**
   - Set typography, colors, and spacing for event titles

3. **Subtitle Style**
   - Configure subtitle appearance and spacing

4. **Button Style**
   - Customize button colors, typography, padding, and hover effects

5. **Navigation Style**
   - Style arrows and dots to match your design

## Troubleshooting

### Common Issues

1. **Widget Not Appearing**
   - Ensure Elementor is installed and activated
   - Check that the plugin is activated
   - Clear any caching plugins

2. **Slider Not Working**
   - Check browser console for JavaScript errors
   - Ensure jQuery is loaded
   - Verify no theme conflicts

3. **Styling Issues**
   - Check for theme CSS conflicts
   - Use browser developer tools to inspect elements
   - Ensure proper CSS loading

4. **Performance Issues**
   - Optimize images before uploading
   - Limit the number of slides for better performance
   - Use caching plugins appropriately

### Getting Help

If you encounter issues:

1. **Check Requirements**
   - Verify all system requirements are met

2. **Plugin Conflicts**
   - Deactivate other plugins temporarily to identify conflicts

3. **Theme Compatibility**
   - Test with a default WordPress theme

4. **Browser Testing**
   - Test in different browsers to identify browser-specific issues

## Best Practices

### Performance Optimization

1. **Image Optimization**
   - Use optimized images (WebP format recommended)
   - Keep image file sizes under 500KB
   - Use consistent image dimensions

2. **Content Management**
   - Limit slides to 10-15 for optimal performance
   - Use concise titles and descriptions
   - Test loading times regularly

### Design Guidelines

1. **Responsive Design**
   - Test on various screen sizes
   - Use appropriate slide counts for different devices
   - Ensure text remains readable on mobile

2. **Accessibility**
   - Use descriptive alt text for images
   - Ensure sufficient color contrast
   - Test with keyboard navigation

3. **User Experience**
   - Keep autoplay speed reasonable (3-5 seconds)
   - Provide clear navigation options
   - Use consistent styling across slides

## Updates and Maintenance

### Keeping the Plugin Updated

1. **Check for Updates**
   - Regularly check for plugin updates
   - Test updates on staging sites first

2. **Backup Before Updates**
   - Always backup your site before updating
   - Keep recent backups available

3. **Monitor Performance**
   - Check site performance after updates
   - Test all slider functionality

## Support

For additional support and documentation, please refer to:
- Plugin documentation
- WordPress support forums
- Elementor community resources

Remember to always test changes on a staging site before applying them to your live website.
