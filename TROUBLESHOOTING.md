# Event Slider Plugin Troubleshooting Guide

## Common Installation Issues

### "Critical Error" on Plugin Activation

**Symptoms:**
- White screen with "There has been a critical error on this website"
- Plugin fails to activate
- Website becomes inaccessible

**Causes & Solutions:**

#### 1. PHP Syntax Errors (Most Common)
**Cause:** PHP version compatibility issues or syntax errors in the code.

**Solution:**
- **Use the fixed version:** Download `event-slider-v1.0.1.zip` from the `dist` folder
- **Check PHP version:** Ensure your server runs PHP 7.4 or higher
- **Enable error reporting:** Add this to your `wp-config.php` temporarily:
  ```php
  define('WP_DEBUG', true);
  define('WP_DEBUG_LOG', true);
  define('WP_DEBUG_DISPLAY', false);
  ```

#### 2. Missing Elementor Plugin
**Cause:** Event Slider requires Elementor to be installed and activated.

**Solution:**
1. Install Elementor from WordPress.org
2. Activate Elementor
3. Then activate Event Slider

#### 3. PHP Version Too Old
**Cause:** Server running PHP version below 7.4.

**Solution:**
- Contact your hosting provider to upgrade PHP to 7.4+
- Or use a hosting service that supports modern PHP versions

#### 4. Memory Limit Issues
**Cause:** PHP memory limit too low.

**Solution:**
- Increase PHP memory limit to at least 256MB
- Add to `wp-config.php`: `ini_set('memory_limit', '256M');`

### Plugin Activation Steps (Safe Method)

1. **Backup your site first**
2. **Check requirements:**
   - WordPress 5.0+
   - PHP 7.4+
   - Elementor 3.0+

3. **Install via WordPress Admin:**
   - Go to Plugins > Add New
   - Click "Upload Plugin"
   - Choose `event-slider-v1.0.1.zip`
   - Click "Install Now"
   - **Don't activate yet**

4. **Verify installation:**
   - Check if files are in `/wp-content/plugins/event-slider/`
   - Ensure Elementor is active

5. **Activate safely:**
   - Click "Activate" on the Event Slider plugin
   - If error occurs, immediately deactivate

### Recovery from Critical Error

If your site is showing a critical error:

#### Method 1: Via WordPress Admin (if accessible)
1. Go to Plugins page
2. Deactivate "Event Slider" plugin
3. Site should return to normal

#### Method 2: Via FTP/File Manager
1. Connect to your website via FTP or hosting file manager
2. Navigate to `/wp-content/plugins/`
3. Rename `event-slider` folder to `event-slider-disabled`
4. Site should return to normal

#### Method 3: Via wp-config.php
1. Add this line to `wp-config.php`:
   ```php
   define('WP_DEBUG', true);
   ```
2. Check error logs in `/wp-content/debug.log`
3. Share error details for specific help

## Widget Not Appearing in Elementor

### Symptoms:
- Plugin activated successfully
- But "Event Slider" widget not visible in Elementor

### Solutions:

1. **Clear Elementor Cache:**
   - Go to Elementor > Tools > Regenerate CSS
   - Clear any caching plugins

2. **Check Widget Category:**
   - Look for "Event Slider" category in Elementor widgets panel
   - Widget should appear under this category

3. **Refresh Elementor:**
   - Edit a page with Elementor
   - Press Ctrl+F5 (hard refresh)

## Slider Not Working on Frontend

### Symptoms:
- Widget appears in Elementor
- But slider doesn't function on frontend

### Solutions:

1. **Check JavaScript Console:**
   - Press F12 in browser
   - Look for JavaScript errors
   - Common issues: jQuery conflicts

2. **Theme Compatibility:**
   - Test with a default WordPress theme (Twenty Twenty-Three)
   - If works with default theme, contact theme developer

3. **Plugin Conflicts:**
   - Deactivate all other plugins temporarily
   - Test if slider works
   - Reactivate plugins one by one to find conflict

## Performance Issues

### Symptoms:
- Slow loading times
- High server resource usage

### Solutions:

1. **Optimize Images:**
   - Use WebP format when possible
   - Keep images under 500KB
   - Use consistent dimensions

2. **Limit Slides:**
   - Keep slides under 10-15 for optimal performance
   - Use pagination for more events

3. **Caching:**
   - Use a caching plugin
   - Enable browser caching

## Styling Issues

### Symptoms:
- Slider appears but styling is broken
- Elements overlapping or misaligned

### Solutions:

1. **CSS Conflicts:**
   - Check browser developer tools
   - Look for CSS conflicts with theme
   - Add custom CSS if needed

2. **Responsive Issues:**
   - Test on different screen sizes
   - Adjust responsive settings in widget

## Getting Help

### Before Asking for Help:

1. **Gather Information:**
   - WordPress version
   - PHP version
   - Elementor version
   - Active theme name
   - List of active plugins

2. **Check Error Logs:**
   - Enable WordPress debugging
   - Check `/wp-content/debug.log`
   - Note exact error messages

3. **Test Environment:**
   - Try on a staging site first
   - Test with default theme
   - Test with minimal plugins

### Where to Get Help:

1. **WordPress Support Forums**
2. **Elementor Community**
3. **Your hosting provider** (for server-related issues)

### Information to Include:

- Exact error message
- Steps to reproduce the issue
- WordPress/PHP/Elementor versions
- Active theme and plugins
- Screenshots of the issue

## Prevention Tips

1. **Always backup before installing plugins**
2. **Test on staging sites first**
3. **Keep WordPress, themes, and plugins updated**
4. **Use reputable hosting with modern PHP versions**
5. **Monitor error logs regularly**

## Quick Fixes Summary

| Issue | Quick Fix |
|-------|-----------|
| Critical Error | Deactivate plugin via FTP |
| Widget Missing | Clear Elementor cache |
| Slider Not Working | Check JavaScript console |
| Styling Broken | Test with default theme |
| Slow Performance | Optimize images, limit slides |

Remember: Most issues are caused by server configuration, theme conflicts, or plugin conflicts rather than the Event Slider plugin itself.
