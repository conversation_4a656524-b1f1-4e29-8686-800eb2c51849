/**
 * Event Slider Editor JavaScript
 */

(function($) {
    'use strict';

    /**
     * Initialize editor functionality
     */
    $(window).on('elementor:init', function() {
        
        // Add custom CSS for better editor experience
        elementor.hooks.addAction('panel/open_editor/widget/event-slider', function(panel, model, view) {
            // Add any editor-specific functionality here
            console.log('Event Slider widget opened in editor');
        });

        // Handle live preview updates
        elementor.hooks.addAction('frontend/element_ready/event-slider.default', function($scope) {
            var $slider = $scope.find('.event-slider-container .event-slider-wrapper');
            
            if ($slider.length > 0) {
                // Destroy existing slider instance if it exists
                if ($slider.data('event-slider-initialized')) {
                    $slider.eventSlider('destroy');
                    $slider.removeData('event-slider-initialized');
                }
                
                // Initialize new slider
                setTimeout(function() {
                    $slider.eventSlider();
                    $slider.data('event-slider-initialized', true);
                }, 100);
            }
        });

    });

    /**
     * Editor preview functionality
     */
    $(document).ready(function() {
        
        // Handle Elementor editor mode
        if (window.elementorFrontend && window.elementorFrontend.isEditMode()) {
            
            // Refresh sliders when content changes
            $(document).on('input change', '.elementor-control input, .elementor-control select, .elementor-control textarea', function() {
                setTimeout(function() {
                    $('.event-slider-container .event-slider-wrapper').each(function() {
                        var $slider = $(this);
                        if ($slider.data('event-slider-initialized')) {
                            $slider.eventSlider('destroy');
                            $slider.removeData('event-slider-initialized');
                            
                            setTimeout(function() {
                                $slider.eventSlider();
                                $slider.data('event-slider-initialized', true);
                            }, 100);
                        }
                    });
                }, 500);
            });
        }
    });

})(jQuery);
