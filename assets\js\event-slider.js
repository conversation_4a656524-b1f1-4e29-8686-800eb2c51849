/**
 * Event Slider JavaScript
 */

(function($) {
    'use strict';

    /**
     * Event Slider Plugin
     */
    $.fn.eventSlider = function(options) {
        return this.each(function() {
            new EventSlider(this, options);
        });
    };

    /**
     * Event Slider Class
     */
    function EventSlider(element, options) {
        this.element = element;
        this.$element = $(element);
        this.options = $.extend({}, this.defaults, options);
        this.currentSlide = 0;
        this.totalSlides = 0;
        this.isAnimating = false;
        this.autoplayTimer = null;
        this.touchStartX = 0;
        this.touchEndX = 0;

        this.init();
    }

    EventSlider.prototype = {
        defaults: {
            autoplay: true,
            autoplaySpeed: 3000,
            infinite: true,
            arrows: true,
            dots: true,
            slidesToShow: 3,
            slidesToScroll: 1,
            responsive: [
                {
                    breakpoint: 768,
                    settings: {
                        slidesToShow: 2,
                        slidesToScroll: 1
                    }
                },
                {
                    breakpoint: 480,
                    settings: {
                        slidesToShow: 1,
                        slidesToScroll: 1
                    }
                }
            ]
        },

        init: function() {
            this.setupSlider();
            this.bindEvents();
            this.updateResponsiveSettings();
            this.goToSlide(0);
            
            if (this.options.autoplay) {
                this.startAutoplay();
            }

            // Add fade-in animation
            this.$element.addClass('event-slider-fade-in');
        },

        setupSlider: function() {
            this.$slides = this.$element.find('.event-slider-slide');
            this.totalSlides = this.$slides.length;

            if (this.totalSlides === 0) {
                return;
            }

            // Set up slide widths
            this.updateSlideWidths();

            // Setup navigation
            if (this.options.arrows) {
                this.setupArrows();
            }

            // Setup dots
            if (this.options.dots) {
                this.setupDots();
            }

            // Setup touch events
            this.setupTouchEvents();
        },

        updateSlideWidths: function() {
            var slideWidth = 100 / this.options.slidesToShow;
            this.$slides.css('width', slideWidth + '%');
        },

        setupArrows: function() {
            this.$prevArrow = this.$element.find('.event-slider-prev');
            this.$nextArrow = this.$element.find('.event-slider-next');

            if (this.$prevArrow.length === 0) {
                this.$prevArrow = $('<button class="event-slider-prev" aria-label="Previous slide"><i class="fas fa-chevron-left"></i></button>');
                this.$element.append(this.$prevArrow);
            }

            if (this.$nextArrow.length === 0) {
                this.$nextArrow = $('<button class="event-slider-next" aria-label="Next slide"><i class="fas fa-chevron-right"></i></button>');
                this.$element.append(this.$nextArrow);
            }
        },

        setupDots: function() {
            this.$dotsContainer = this.$element.find('.event-slider-dots');
            
            if (this.$dotsContainer.length === 0) {
                this.$dotsContainer = $('<div class="event-slider-dots"></div>');
                this.$element.append(this.$dotsContainer);
            }

            this.$dotsContainer.empty();

            var dotsCount = Math.ceil(this.totalSlides / this.options.slidesToScroll);
            for (var i = 0; i < dotsCount; i++) {
                var $dot = $('<button class="event-slider-dot" data-slide="' + i + '" aria-label="Go to slide ' + (i + 1) + '"></button>');
                this.$dotsContainer.append($dot);
            }

            this.$dots = this.$dotsContainer.find('.event-slider-dot');
        },

        setupTouchEvents: function() {
            this.$element.on('touchstart', this.handleTouchStart.bind(this));
            this.$element.on('touchend', this.handleTouchEnd.bind(this));
        },

        bindEvents: function() {
            var self = this;

            // Arrow navigation
            this.$element.on('click', '.event-slider-prev', function(e) {
                e.preventDefault();
                self.prevSlide();
            });

            this.$element.on('click', '.event-slider-next', function(e) {
                e.preventDefault();
                self.nextSlide();
            });

            // Dot navigation
            this.$element.on('click', '.event-slider-dot', function(e) {
                e.preventDefault();
                var slideIndex = parseInt($(this).data('slide'), 10);
                self.goToSlide(slideIndex * self.options.slidesToScroll);
            });

            // Pause autoplay on hover
            this.$element.on('mouseenter', function() {
                self.pauseAutoplay();
            });

            this.$element.on('mouseleave', function() {
                if (self.options.autoplay) {
                    self.startAutoplay();
                }
            });

            // Keyboard navigation
            this.$element.on('keydown', function(e) {
                if (e.keyCode === 37) { // Left arrow
                    self.prevSlide();
                } else if (e.keyCode === 39) { // Right arrow
                    self.nextSlide();
                }
            });

            // Window resize
            $(window).on('resize', function() {
                self.updateResponsiveSettings();
                self.updateSlideWidths();
                self.goToSlide(self.currentSlide);
            });
        },

        updateResponsiveSettings: function() {
            var windowWidth = $(window).width();
            var newSettings = $.extend({}, this.defaults);

            // Apply responsive settings
            for (var i = 0; i < this.options.responsive.length; i++) {
                var responsive = this.options.responsive[i];
                if (windowWidth <= responsive.breakpoint) {
                    newSettings = $.extend(newSettings, responsive.settings);
                }
            }

            // Update current options
            this.options.slidesToShow = newSettings.slidesToShow;
            this.options.slidesToScroll = newSettings.slidesToScroll;
        },

        goToSlide: function(slideIndex) {
            if (this.isAnimating || this.totalSlides === 0) {
                return;
            }

            // Normalize slide index
            if (this.options.infinite) {
                if (slideIndex < 0) {
                    slideIndex = this.totalSlides - this.options.slidesToShow;
                } else if (slideIndex >= this.totalSlides) {
                    slideIndex = 0;
                }
            } else {
                slideIndex = Math.max(0, Math.min(slideIndex, this.totalSlides - this.options.slidesToShow));
            }

            this.currentSlide = slideIndex;
            this.isAnimating = true;

            // Calculate transform
            var translateX = -(slideIndex * (100 / this.options.slidesToShow));
            
            // Apply transform
            this.$element.find('.event-slider-wrapper').css({
                'transform': 'translateX(' + translateX + '%)',
                'transition': 'transform 0.3s ease-in-out'
            });

            // Update dots
            this.updateDots();

            // Update arrows
            this.updateArrows();

            // Reset animation flag
            setTimeout(() => {
                this.isAnimating = false;
            }, 300);
        },

        nextSlide: function() {
            var nextIndex = this.currentSlide + this.options.slidesToScroll;
            this.goToSlide(nextIndex);
        },

        prevSlide: function() {
            var prevIndex = this.currentSlide - this.options.slidesToScroll;
            this.goToSlide(prevIndex);
        },

        updateDots: function() {
            if (!this.$dots) return;

            this.$dots.removeClass('active');
            var activeDotIndex = Math.floor(this.currentSlide / this.options.slidesToScroll);
            this.$dots.eq(activeDotIndex).addClass('active');
        },

        updateArrows: function() {
            if (!this.options.arrows) return;

            if (this.options.infinite) {
                this.$prevArrow.show();
                this.$nextArrow.show();
            } else {
                // Hide/show arrows based on current position
                if (this.currentSlide === 0) {
                    this.$prevArrow.hide();
                } else {
                    this.$prevArrow.show();
                }

                if (this.currentSlide >= this.totalSlides - this.options.slidesToShow) {
                    this.$nextArrow.hide();
                } else {
                    this.$nextArrow.show();
                }
            }
        },

        startAutoplay: function() {
            if (!this.options.autoplay) return;

            this.pauseAutoplay();
            this.autoplayTimer = setInterval(() => {
                this.nextSlide();
            }, this.options.autoplaySpeed);
        },

        pauseAutoplay: function() {
            if (this.autoplayTimer) {
                clearInterval(this.autoplayTimer);
                this.autoplayTimer = null;
            }
        },

        handleTouchStart: function(e) {
            this.touchStartX = e.originalEvent.touches[0].clientX;
        },

        handleTouchEnd: function(e) {
            this.touchEndX = e.originalEvent.changedTouches[0].clientX;
            this.handleSwipe();
        },

        handleSwipe: function() {
            var swipeThreshold = 50;
            var diff = this.touchStartX - this.touchEndX;

            if (Math.abs(diff) > swipeThreshold) {
                if (diff > 0) {
                    // Swipe left - next slide
                    this.nextSlide();
                } else {
                    // Swipe right - previous slide
                    this.prevSlide();
                }
            }
        },

        destroy: function() {
            this.pauseAutoplay();
            this.$element.off();
            $(window).off('resize');
            this.$element.find('.event-slider-wrapper').css({
                'transform': '',
                'transition': ''
            });
        }
    };

    // Initialize sliders on document ready
    $(document).ready(function() {
        $('.event-slider-container .event-slider-wrapper').each(function() {
            if (!$(this).data('event-slider-initialized')) {
                $(this).eventSlider();
                $(this).data('event-slider-initialized', true);
            }
        });
    });

})(jQuery);
