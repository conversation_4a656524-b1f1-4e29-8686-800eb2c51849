@echo off
REM Event Slider Plugin Build Script for Windows
REM This script creates a clean, installable WordPress plugin zip file

setlocal enabledelayedexpansion

REM Configuration
set PLUGIN_NAME=event-slider
set VERSION=1.0.0
set OUTPUT_DIR=dist
set CLEAN=false

REM Colors (limited in batch)
set GREEN=[92m
set YELLOW=[93m
set CYAN=[96m
set RED=[91m
set GRAY=[37m
set RESET=[0m

echo %GREEN%Event Slider Plugin Build Script%RESET%
echo %GREEN%=================================%RESET%
echo %YELLOW%Plugin: %PLUGIN_NAME%%RESET%
echo %YELLOW%Version: %VERSION%%RESET%
echo.

REM Set up paths
set PLUGIN_DIR=%CD%
set BUILD_DIR=%PLUGIN_DIR%\%OUTPUT_DIR%
set TEMP_DIR=%BUILD_DIR%\temp
set PLUGIN_TEMP_DIR=%TEMP_DIR%\%PLUGIN_NAME%
set ZIP_FILE=%BUILD_DIR%\%PLUGIN_NAME%-v%VERSION%.zip

REM Check if PowerShell is available
powershell -Command "Write-Host 'PowerShell available'" >nul 2>&1
if errorlevel 1 (
    echo %RED%Error: PowerShell is required but not available%RESET%
    echo Please install PowerShell or use the PowerShell script directly
    pause
    exit /b 1
)

REM Clean previous builds if directory exists
if exist "%BUILD_DIR%" (
    echo %CYAN%Cleaning previous builds...%RESET%
    rmdir /s /q "%BUILD_DIR%" 2>nul
    echo %GREEN%✓ Cleaned build directory%RESET%
)

REM Create build directories
echo %CYAN%Creating build directories...%RESET%
mkdir "%BUILD_DIR%" 2>nul
mkdir "%TEMP_DIR%" 2>nul
mkdir "%PLUGIN_TEMP_DIR%" 2>nul
echo %GREEN%✓ Build directories created%RESET%

REM Copy main plugin files
echo %CYAN%Copying plugin files...%RESET%

if exist "event-slider.php" (
    copy "event-slider.php" "%PLUGIN_TEMP_DIR%\" >nul
    echo %GRAY%  ✓ Copied event-slider.php%RESET%
) else (
    echo %YELLOW%  ⚠ Warning: event-slider.php not found%RESET%
)

if exist "readme.txt" (
    copy "readme.txt" "%PLUGIN_TEMP_DIR%\" >nul
    echo %GRAY%  ✓ Copied readme.txt%RESET%
) else (
    echo %YELLOW%  ⚠ Warning: readme.txt not found%RESET%
)

REM Copy directories
if exist "includes" (
    xcopy "includes" "%PLUGIN_TEMP_DIR%\includes\" /e /i /q >nul
    echo %GRAY%  ✓ Copied directory includes%RESET%
) else (
    echo %YELLOW%  ⚠ Warning: Directory includes not found%RESET%
)

if exist "assets" (
    xcopy "assets" "%PLUGIN_TEMP_DIR%\assets\" /e /i /q >nul
    echo %GRAY%  ✓ Copied directory assets%RESET%
) else (
    echo %YELLOW%  ⚠ Warning: Directory assets not found%RESET%
)

echo %GREEN%✓ Plugin files copied%RESET%

REM Clean up excluded files using PowerShell
echo %CYAN%Cleaning excluded files...%RESET%
powershell -Command "& {
    $excludePatterns = @('*.md', '*.sh', '*.ps1', '*.bat', 'build.js', 'package.json', 'composer.json', '*.log', '*.tmp', '.git*', '.DS_Store', 'Thumbs.db', 'node_modules', 'vendor', 'dist', '*.zip', '.vscode', '.idea', '*.bak', '*.orig')
    Get-ChildItem '%PLUGIN_TEMP_DIR%' -Recurse | ForEach-Object {
        $file = $_.FullName
        $name = $_.Name
        foreach ($pattern in $excludePatterns) {
            if ($name -like $pattern) {
                Remove-Item $file -Force -Recurse -ErrorAction SilentlyContinue
                $relativePath = $file.Replace('%PLUGIN_TEMP_DIR%\', '')
                Write-Host '    - Excluded' $relativePath -ForegroundColor DarkGray
                break
            }
        }
    }
}"

REM Update version in main plugin file
echo %CYAN%Updating version information...%RESET%
if exist "%PLUGIN_TEMP_DIR%\event-slider.php" (
    powershell -Command "& {
        $file = '%PLUGIN_TEMP_DIR%\event-slider.php'
        $content = Get-Content $file -Raw
        $content = $content -replace 'Version: [\d\.]+', 'Version: %VERSION%'
        $content = $content -replace 'EVENT_SLIDER_VERSION'', ''[\d\.]+''', 'EVENT_SLIDER_VERSION'', ''%VERSION%'''
        Set-Content $file -Value $content -NoNewline
    }"
    echo %GREEN%✓ Version updated to %VERSION%%RESET%
) else (
    echo %YELLOW%⚠ Warning: Main plugin file not found for version update%RESET%
)

REM Create zip file using PowerShell
echo %CYAN%Creating zip file...%RESET%
if exist "%ZIP_FILE%" del "%ZIP_FILE%"

powershell -Command "Compress-Archive -Path '%TEMP_DIR%\*' -DestinationPath '%ZIP_FILE%' -Force"
if errorlevel 1 (
    echo %RED%✗ Error creating zip file%RESET%
    pause
    exit /b 1
)
echo %GREEN%✓ Zip file created%RESET%

REM Clean up temporary files
echo %CYAN%Cleaning up...%RESET%
rmdir /s /q "%TEMP_DIR%" 2>nul
echo %GREEN%✓ Temporary files cleaned%RESET%

REM Display results
echo.
echo %GREEN%Build completed successfully!%RESET%
echo %GREEN%=================================%RESET%
echo %YELLOW%Plugin package: %ZIP_FILE%%RESET%

if exist "%ZIP_FILE%" (
    for %%A in ("%ZIP_FILE%") do (
        set /a "size=%%~zA/1024"
        echo %YELLOW%Package size: !size! KB%RESET%
    )
    
    echo.
    echo %CYAN%Installation Instructions:%RESET%
    echo %GRAY%1. Go to WordPress Admin ^> Plugins ^> Add New%RESET%
    echo %GRAY%2. Click 'Upload Plugin'%RESET%
    echo %GRAY%3. Choose the zip file: %ZIP_FILE%%RESET%
    echo %GRAY%4. Click 'Install Now' and then 'Activate'%RESET%
    
    echo.
    echo %CYAN%Package contents:%RESET%
    powershell -Command "& {
        Add-Type -AssemblyName System.IO.Compression.FileSystem
        $zip = [System.IO.Compression.ZipFile]::OpenRead('%ZIP_FILE%')
        $zip.Entries | ForEach-Object {
            Write-Host '  ' $_.FullName -ForegroundColor Gray
        }
        $zip.Dispose()
    }"
) else (
    echo %RED%✗ Error: Zip file was not created%RESET%
    pause
    exit /b 1
)

echo.
echo %GREEN%Build script completed!%RESET%
echo.
echo Press any key to exit...
pause >nul
