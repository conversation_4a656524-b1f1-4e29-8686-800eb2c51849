#!/usr/bin/env node

/**
 * Event Slider Plugin Build Script (Node.js)
 * This script creates a clean, installable WordPress plugin zip file
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const config = {
    pluginName: 'event-slider',
    version: '1.0.0',
    outputDir: 'dist',
    clean: false
};

// Colors for console output
const colors = {
    reset: '\x1b[0m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    cyan: '\x1b[36m',
    gray: '\x1b[37m',
    darkGray: '\x1b[90m'
};

// Parse command line arguments
const args = process.argv.slice(2);
for (let i = 0; i < args.length; i++) {
    switch (args[i]) {
        case '-v':
        case '--version':
            config.version = args[++i];
            break;
        case '-o':
        case '--output':
            config.outputDir = args[++i];
            break;
        case '-c':
        case '--clean':
            config.clean = true;
            break;
        case '-h':
        case '--help':
            console.log('Event Slider Plugin Build Script');
            console.log('Usage: node build.js [OPTIONS]');
            console.log('');
            console.log('Options:');
            console.log('  -v, --version VERSION    Set plugin version (default: 1.0.0)');
            console.log('  -o, --output DIR         Set output directory (default: dist)');
            console.log('  -c, --clean              Clean previous builds');
            console.log('  -h, --help               Show this help message');
            console.log('');
            console.log('Example:');
            console.log('  node build.js --version 1.2.0 --clean');
            process.exit(0);
        default:
            console.error(`Unknown option: ${args[i]}`);
            console.error('Use -h or --help for usage information');
            process.exit(1);
    }
}

// Set up paths
const pluginDir = process.cwd();
const buildDir = path.join(pluginDir, config.outputDir);
const tempDir = path.join(buildDir, 'temp');
const pluginTempDir = path.join(tempDir, config.pluginName);
const zipFile = path.join(buildDir, `${config.pluginName}-v${config.version}.zip`);

// Utility functions
function log(message, color = colors.reset) {
    console.log(`${color}${message}${colors.reset}`);
}

function ensureDir(dir) {
    if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
    }
}

function removeDir(dir) {
    if (fs.existsSync(dir)) {
        fs.rmSync(dir, { recursive: true, force: true });
    }
}

function copyFile(src, dest) {
    const destDir = path.dirname(dest);
    ensureDir(destDir);
    fs.copyFileSync(src, dest);
}

function copyDir(src, dest) {
    ensureDir(dest);
    const items = fs.readdirSync(src);
    
    for (const item of items) {
        const srcPath = path.join(src, item);
        const destPath = path.join(dest, item);
        const stat = fs.statSync(srcPath);
        
        if (stat.isDirectory()) {
            copyDir(srcPath, destPath);
        } else {
            copyFile(srcPath, destPath);
        }
    }
}

function shouldExclude(filePath) {
    const excludePatterns = [
        /\.md$/,
        /\.sh$/,
        /\.ps1$/,
        /\.bat$/,
        /^build\.js$/,
        /^package\.json$/,
        /^composer\.json$/,
        /\.log$/,
        /\.tmp$/,
        /^\.git/,
        /^\.DS_Store$/,
        /^Thumbs\.db$/,
        /^node_modules$/,
        /^vendor$/,
        /^dist$/,
        /\.zip$/,
        /^\.vscode$/,
        /^\.idea$/,
        /\.bak$/,
        /\.orig$/
    ];
    
    const fileName = path.basename(filePath);
    return excludePatterns.some(pattern => pattern.test(fileName));
}

function removeExcludedFiles(dir) {
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
        const itemPath = path.join(dir, item);
        const stat = fs.statSync(itemPath);
        
        if (stat.isDirectory()) {
            removeExcludedFiles(itemPath);
            // Remove directory if empty
            try {
                const remaining = fs.readdirSync(itemPath);
                if (remaining.length === 0) {
                    fs.rmdirSync(itemPath);
                }
            } catch (e) {
                // Directory not empty or other error, ignore
            }
        } else if (shouldExclude(itemPath)) {
            fs.unlinkSync(itemPath);
            const relativePath = path.relative(pluginTempDir, itemPath);
            log(`    - Excluded ${relativePath}`, colors.darkGray);
        }
    }
}

function updateVersion(filePath, version) {
    let content = fs.readFileSync(filePath, 'utf8');
    
    // Update version in plugin header
    content = content.replace(/Version: [\d\.]+/, `Version: ${version}`);
    
    // Update version constant
    content = content.replace(/EVENT_SLIDER_VERSION', '[\d\.]+'/, `EVENT_SLIDER_VERSION', '${version}'`);
    
    fs.writeFileSync(filePath, content);
}

function createZip(sourceDir, outputFile) {
    try {
        // Try using system zip command first
        const zipCommand = process.platform === 'win32' ? 
            `powershell Compress-Archive -Path "${sourceDir}\\*" -DestinationPath "${outputFile}" -Force` :
            `cd "${path.dirname(sourceDir)}" && zip -r "${path.basename(outputFile)}" "${path.basename(sourceDir)}"`;
        
        execSync(zipCommand, { stdio: 'pipe' });
        
        if (process.platform !== 'win32') {
            // Move zip file to correct location
            const tempZip = path.join(path.dirname(sourceDir), path.basename(outputFile));
            if (fs.existsSync(tempZip) && tempZip !== outputFile) {
                fs.renameSync(tempZip, outputFile);
            }
        }
        
        return true;
    } catch (error) {
        log(`Error creating zip: ${error.message}`, colors.red);
        return false;
    }
}

function getFileSize(filePath) {
    const stats = fs.statSync(filePath);
    const bytes = stats.size;
    
    if (bytes < 1024) return `${bytes} B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
}

// Main build process
function build() {
    log('Event Slider Plugin Build Script', colors.green);
    log('=================================', colors.green);
    log(`Plugin: ${config.pluginName}`, colors.yellow);
    log(`Version: ${config.version}`, colors.yellow);
    log(`Output: ${zipFile}`, colors.yellow);
    log('');

    // Clean previous builds if requested
    if (config.clean && fs.existsSync(buildDir)) {
        log('Cleaning previous builds...', colors.cyan);
        removeDir(buildDir);
        log('✓ Cleaned build directory', colors.green);
    }

    // Create build directories
    log('Creating build directories...', colors.cyan);
    ensureDir(buildDir);
    ensureDir(tempDir);
    ensureDir(pluginTempDir);
    log('✓ Build directories created', colors.green);

    // Define files and directories to include
    const includeFiles = ['event-slider.php', 'readme.txt'];
    const includeDirs = ['includes', 'assets'];

    // Copy main plugin files
    log('Copying plugin files...', colors.cyan);
    for (const file of includeFiles) {
        const srcPath = path.join(pluginDir, file);
        if (fs.existsSync(srcPath)) {
            const destPath = path.join(pluginTempDir, file);
            copyFile(srcPath, destPath);
            log(`  ✓ Copied ${file}`, colors.gray);
        } else {
            log(`  ⚠ Warning: ${file} not found`, colors.yellow);
        }
    }

    // Copy directories
    for (const dir of includeDirs) {
        const srcPath = path.join(pluginDir, dir);
        if (fs.existsSync(srcPath)) {
            const destPath = path.join(pluginTempDir, dir);
            copyDir(srcPath, destPath);
            log(`  ✓ Copied directory ${dir}`, colors.gray);
            
            // Remove excluded files
            removeExcludedFiles(destPath);
        } else {
            log(`  ⚠ Warning: Directory ${dir} not found`, colors.yellow);
        }
    }

    log('✓ Plugin files copied', colors.green);

    // Update version in main plugin file
    log('Updating version information...', colors.cyan);
    const mainPluginFile = path.join(pluginTempDir, 'event-slider.php');
    if (fs.existsSync(mainPluginFile)) {
        updateVersion(mainPluginFile, config.version);
        log(`✓ Version updated to ${config.version}`, colors.green);
    } else {
        log('⚠ Warning: Main plugin file not found for version update', colors.yellow);
    }

    // Create zip file
    log('Creating zip file...', colors.cyan);
    if (fs.existsSync(zipFile)) {
        fs.unlinkSync(zipFile);
    }

    if (createZip(tempDir, zipFile)) {
        log('✓ Zip file created successfully', colors.green);
    } else {
        log('✗ Error creating zip file', colors.red);
        process.exit(1);
    }

    // Clean up temporary files
    log('Cleaning up...', colors.cyan);
    removeDir(tempDir);
    log('✓ Temporary files cleaned', colors.green);

    // Display results
    log('', colors.reset);
    log('Build completed successfully!', colors.green);
    log('=================================', colors.green);
    log(`Plugin package: ${zipFile}`, colors.yellow);

    if (fs.existsSync(zipFile)) {
        const zipSize = getFileSize(zipFile);
        log(`Package size: ${zipSize}`, colors.yellow);
        
        log('', colors.reset);
        log('Installation Instructions:', colors.cyan);
        log('1. Go to WordPress Admin > Plugins > Add New', colors.gray);
        log('2. Click \'Upload Plugin\'', colors.gray);
        log(`3. Choose the zip file: ${zipFile}`, colors.gray);
        log('4. Click \'Install Now\' and then \'Activate\'', colors.gray);
    } else {
        log('✗ Error: Zip file was not created', colors.red);
        process.exit(1);
    }

    log('', colors.reset);
    log('Build script completed!', colors.green);
}

// Run the build
try {
    build();
} catch (error) {
    log(`Build failed: ${error.message}`, colors.red);
    process.exit(1);
}
