# Event Slider Plugin Build Script for Windows PowerShell
# This script creates a clean, installable WordPress plugin zip file

param(
    [string]$Version = "1.0.0",
    [string]$OutputDir = "dist",
    [switch]$Clean = $false
)

# Configuration
$PluginName = "event-slider"
$PluginDir = Get-Location
$BuildDir = Join-Path $PluginDir $OutputDir
$TempDir = Join-Path $BuildDir "temp"
$ZipFile = Join-Path $BuildDir "$PluginName-v$Version.zip"

Write-Host "Event Slider Plugin Build Script" -ForegroundColor Green
Write-Host "=================================" -ForegroundColor Green
Write-Host "Plugin: $PluginName" -ForegroundColor Yellow
Write-Host "Version: $Version" -ForegroundColor Yellow
Write-Host "Output: $ZipFile" -ForegroundColor Yellow
Write-Host ""

# Clean previous builds if requested
if ($Clean -and (Test-Path $BuildDir)) {
    Write-Host "Cleaning previous builds..." -ForegroundColor Cyan
    Remove-Item $BuildDir -Recurse -Force
    Write-Host "✓ Cleaned build directory" -ForegroundColor Green
}

# Create build directories
Write-Host "Creating build directories..." -ForegroundColor Cyan
if (!(Test-Path $BuildDir)) {
    New-Item -ItemType Directory -Path $BuildDir -Force | Out-Null
}
if (!(Test-Path $TempDir)) {
    New-Item -ItemType Directory -Path $TempDir -Force | Out-Null
}
$PluginTempDir = Join-Path $TempDir $PluginName
if (!(Test-Path $PluginTempDir)) {
    New-Item -ItemType Directory -Path $PluginTempDir -Force | Out-Null
}
Write-Host "✓ Build directories created" -ForegroundColor Green

# Define files and directories to include
$IncludeFiles = @(
    "event-slider.php",
    "readme.txt"
)

$IncludeDirs = @(
    "includes",
    "assets"
)

# Define files and patterns to exclude
$ExcludePatterns = @(
    "*.md",           # Markdown files
    "*.ps1",          # PowerShell scripts
    "*.sh",           # Shell scripts
    "*.bat",          # Batch files
    "build.js",       # Build scripts
    "package.json",   # Node.js files
    "composer.json",  # Composer files
    "*.log",          # Log files
    "*.tmp",          # Temporary files
    ".git*",          # Git files
    ".DS_Store",      # macOS files
    "Thumbs.db",      # Windows files
    "node_modules",   # Node.js dependencies
    "vendor",         # Composer dependencies
    "dist",           # Build output
    "*.zip",          # Zip files
    ".vscode",        # VS Code settings
    ".idea",          # PhpStorm settings
    "*.bak",          # Backup files
    "*.orig"          # Original files
)

# Copy main plugin files
Write-Host "Copying plugin files..." -ForegroundColor Cyan
foreach ($file in $IncludeFiles) {
    if (Test-Path $file) {
        Copy-Item $file -Destination $PluginTempDir -Force
        Write-Host "  ✓ Copied $file" -ForegroundColor Gray
    } else {
        Write-Host "  ⚠ Warning: $file not found" -ForegroundColor Yellow
    }
}

# Copy directories
foreach ($dir in $IncludeDirs) {
    if (Test-Path $dir) {
        $destDir = Join-Path $PluginTempDir $dir
        Copy-Item $dir -Destination $destDir -Recurse -Force
        Write-Host "  ✓ Copied directory $dir" -ForegroundColor Gray
        
        # Remove excluded files from copied directories
        Get-ChildItem $destDir -Recurse | ForEach-Object {
            $relativePath = $_.FullName.Substring($destDir.Length + 1)
            foreach ($pattern in $ExcludePatterns) {
                if ($_.Name -like $pattern -or $relativePath -like $pattern) {
                    Remove-Item $_.FullName -Force -Recurse -ErrorAction SilentlyContinue
                    Write-Host "    - Excluded $relativePath" -ForegroundColor DarkGray
                    break
                }
            }
        }
    } else {
        Write-Host "  ⚠ Warning: Directory $dir not found" -ForegroundColor Yellow
    }
}

Write-Host "✓ Plugin files copied" -ForegroundColor Green

# Update version in main plugin file if needed
Write-Host "Updating version information..." -ForegroundColor Cyan
$mainPluginFile = Join-Path $PluginTempDir "event-slider.php"
if (Test-Path $mainPluginFile) {
    $content = Get-Content $mainPluginFile -Raw
    $content = $content -replace "Version: [\d\.]+", "Version: $Version"
    $content = $content -replace "EVENT_SLIDER_VERSION', '[\d\.]+'", "EVENT_SLIDER_VERSION', '$Version'"
    Set-Content $mainPluginFile -Value $content -NoNewline
    Write-Host "✓ Version updated to $Version" -ForegroundColor Green
} else {
    Write-Host "⚠ Warning: Main plugin file not found for version update" -ForegroundColor Yellow
}

# Create zip file
Write-Host "Creating zip file..." -ForegroundColor Cyan
if (Test-Path $ZipFile) {
    Remove-Item $ZipFile -Force
}

# Use .NET compression if available, otherwise use PowerShell 5.0+ Compress-Archive
try {
    Add-Type -AssemblyName System.IO.Compression.FileSystem
    [System.IO.Compression.ZipFile]::CreateFromDirectory($TempDir, $ZipFile)
    Write-Host "✓ Zip file created using .NET compression" -ForegroundColor Green
} catch {
    try {
        Compress-Archive -Path "$TempDir\*" -DestinationPath $ZipFile -Force
        Write-Host "✓ Zip file created using PowerShell compression" -ForegroundColor Green
    } catch {
        Write-Host "✗ Error creating zip file: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
}

# Clean up temporary files
Write-Host "Cleaning up..." -ForegroundColor Cyan
Remove-Item $TempDir -Recurse -Force
Write-Host "✓ Temporary files cleaned" -ForegroundColor Green

# Display results
Write-Host ""
Write-Host "Build completed successfully!" -ForegroundColor Green
Write-Host "=================================" -ForegroundColor Green
Write-Host "Plugin package: $ZipFile" -ForegroundColor Yellow

if (Test-Path $ZipFile) {
    $zipSize = [math]::Round((Get-Item $ZipFile).Length / 1KB, 2)
    Write-Host "Package size: $zipSize KB" -ForegroundColor Yellow
    
    Write-Host ""
    Write-Host "Installation Instructions:" -ForegroundColor Cyan
    Write-Host "1. Go to WordPress Admin > Plugins > Add New" -ForegroundColor Gray
    Write-Host "2. Click 'Upload Plugin'" -ForegroundColor Gray
    Write-Host "3. Choose the zip file: $ZipFile" -ForegroundColor Gray
    Write-Host "4. Click 'Install Now' and then 'Activate'" -ForegroundColor Gray
    
    Write-Host ""
    Write-Host "Package contents:" -ForegroundColor Cyan
    
    # List contents of zip file
    try {
        Add-Type -AssemblyName System.IO.Compression.FileSystem
        $zip = [System.IO.Compression.ZipFile]::OpenRead($ZipFile)
        $zip.Entries | ForEach-Object {
            Write-Host "  $($_.FullName)" -ForegroundColor Gray
        }
        $zip.Dispose()
    } catch {
        Write-Host "  (Unable to list zip contents)" -ForegroundColor DarkGray
    }
} else {
    Write-Host "✗ Error: Zip file was not created" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "Build script completed!" -ForegroundColor Green
