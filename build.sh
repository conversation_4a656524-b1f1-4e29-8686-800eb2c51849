#!/bin/bash

# Event Slider Plugin Build Script for Linux/Mac
# This script creates a clean, installable WordPress plugin zip file

# Configuration
PLUGIN_NAME="event-slider"
VERSION="1.0.0"
OUTPUT_DIR="dist"
CLEAN=false

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
GRAY='\033[0;37m'
DARK_GRAY='\033[1;30m'
NC='\033[0m' # No Color

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -v|--version)
            VERSION="$2"
            shift 2
            ;;
        -o|--output)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        -c|--clean)
            CLEAN=true
            shift
            ;;
        -h|--help)
            echo "Event Slider Plugin Build Script"
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  -v, --version VERSION    Set plugin version (default: 1.0.0)"
            echo "  -o, --output DIR         Set output directory (default: dist)"
            echo "  -c, --clean              Clean previous builds"
            echo "  -h, --help               Show this help message"
            echo ""
            echo "Example:"
            echo "  $0 --version 1.2.0 --clean"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use -h or --help for usage information"
            exit 1
            ;;
    esac
done

# Set up paths
PLUGIN_DIR="$(pwd)"
BUILD_DIR="$PLUGIN_DIR/$OUTPUT_DIR"
TEMP_DIR="$BUILD_DIR/temp"
PLUGIN_TEMP_DIR="$TEMP_DIR/$PLUGIN_NAME"
ZIP_FILE="$BUILD_DIR/$PLUGIN_NAME-v$VERSION.zip"

echo -e "${GREEN}Event Slider Plugin Build Script${NC}"
echo -e "${GREEN}=================================${NC}"
echo -e "${YELLOW}Plugin: $PLUGIN_NAME${NC}"
echo -e "${YELLOW}Version: $VERSION${NC}"
echo -e "${YELLOW}Output: $ZIP_FILE${NC}"
echo ""

# Clean previous builds if requested
if [ "$CLEAN" = true ] && [ -d "$BUILD_DIR" ]; then
    echo -e "${CYAN}Cleaning previous builds...${NC}"
    rm -rf "$BUILD_DIR"
    echo -e "${GREEN}✓ Cleaned build directory${NC}"
fi

# Create build directories
echo -e "${CYAN}Creating build directories...${NC}"
mkdir -p "$BUILD_DIR"
mkdir -p "$TEMP_DIR"
mkdir -p "$PLUGIN_TEMP_DIR"
echo -e "${GREEN}✓ Build directories created${NC}"

# Define files to include
INCLUDE_FILES=(
    "event-slider.php"
    "readme.txt"
)

# Define directories to include
INCLUDE_DIRS=(
    "includes"
    "assets"
)

# Define patterns to exclude
EXCLUDE_PATTERNS=(
    "*.md"
    "*.sh"
    "*.ps1"
    "*.bat"
    "build.js"
    "package.json"
    "composer.json"
    "*.log"
    "*.tmp"
    ".git*"
    ".DS_Store"
    "Thumbs.db"
    "node_modules"
    "vendor"
    "dist"
    "*.zip"
    ".vscode"
    ".idea"
    "*.bak"
    "*.orig"
)

# Function to check if file should be excluded
should_exclude() {
    local file="$1"
    local basename=$(basename "$file")
    
    for pattern in "${EXCLUDE_PATTERNS[@]}"; do
        if [[ "$basename" == $pattern ]] || [[ "$file" == $pattern ]]; then
            return 0  # Should exclude
        fi
    done
    return 1  # Should not exclude
}

# Copy main plugin files
echo -e "${CYAN}Copying plugin files...${NC}"
for file in "${INCLUDE_FILES[@]}"; do
    if [ -f "$file" ]; then
        cp "$file" "$PLUGIN_TEMP_DIR/"
        echo -e "  ${GRAY}✓ Copied $file${NC}"
    else
        echo -e "  ${YELLOW}⚠ Warning: $file not found${NC}"
    fi
done

# Copy directories
for dir in "${INCLUDE_DIRS[@]}"; do
    if [ -d "$dir" ]; then
        cp -r "$dir" "$PLUGIN_TEMP_DIR/"
        echo -e "  ${GRAY}✓ Copied directory $dir${NC}"
        
        # Remove excluded files from copied directories
        find "$PLUGIN_TEMP_DIR/$dir" -type f | while read -r file; do
            if should_exclude "$file"; then
                relative_path="${file#$PLUGIN_TEMP_DIR/}"
                rm -f "$file"
                echo -e "    ${DARK_GRAY}- Excluded $relative_path${NC}"
            fi
        done
        
        # Remove empty directories
        find "$PLUGIN_TEMP_DIR/$dir" -type d -empty -delete 2>/dev/null
    else
        echo -e "  ${YELLOW}⚠ Warning: Directory $dir not found${NC}"
    fi
done

echo -e "${GREEN}✓ Plugin files copied${NC}"

# Update version in main plugin file
echo -e "${CYAN}Updating version information...${NC}"
MAIN_PLUGIN_FILE="$PLUGIN_TEMP_DIR/event-slider.php"
if [ -f "$MAIN_PLUGIN_FILE" ]; then
    # Update version in plugin header
    sed -i.bak "s/Version: [0-9]\+\.[0-9]\+\.[0-9]\+/Version: $VERSION/" "$MAIN_PLUGIN_FILE"
    # Update version constant
    sed -i.bak "s/EVENT_SLIDER_VERSION', '[0-9]\+\.[0-9]\+\.[0-9]\+'/EVENT_SLIDER_VERSION', '$VERSION'/" "$MAIN_PLUGIN_FILE"
    # Remove backup file
    rm -f "$MAIN_PLUGIN_FILE.bak"
    echo -e "${GREEN}✓ Version updated to $VERSION${NC}"
else
    echo -e "${YELLOW}⚠ Warning: Main plugin file not found for version update${NC}"
fi

# Create zip file
echo -e "${CYAN}Creating zip file...${NC}"
if [ -f "$ZIP_FILE" ]; then
    rm -f "$ZIP_FILE"
fi

# Change to temp directory and create zip
cd "$TEMP_DIR" || exit 1
if command -v zip >/dev/null 2>&1; then
    zip -r "$(basename "$ZIP_FILE")" "$PLUGIN_NAME" >/dev/null 2>&1
    mv "$(basename "$ZIP_FILE")" "$ZIP_FILE"
    echo -e "${GREEN}✓ Zip file created using zip command${NC}"
else
    echo -e "${RED}✗ Error: zip command not found. Please install zip utility.${NC}"
    echo -e "${YELLOW}On Ubuntu/Debian: sudo apt-get install zip${NC}"
    echo -e "${YELLOW}On macOS: zip should be available by default${NC}"
    exit 1
fi

# Return to original directory
cd "$PLUGIN_DIR" || exit 1

# Clean up temporary files
echo -e "${CYAN}Cleaning up...${NC}"
rm -rf "$TEMP_DIR"
echo -e "${GREEN}✓ Temporary files cleaned${NC}"

# Display results
echo ""
echo -e "${GREEN}Build completed successfully!${NC}"
echo -e "${GREEN}=================================${NC}"
echo -e "${YELLOW}Plugin package: $ZIP_FILE${NC}"

if [ -f "$ZIP_FILE" ]; then
    ZIP_SIZE=$(du -h "$ZIP_FILE" | cut -f1)
    echo -e "${YELLOW}Package size: $ZIP_SIZE${NC}"
    
    echo ""
    echo -e "${CYAN}Installation Instructions:${NC}"
    echo -e "${GRAY}1. Go to WordPress Admin > Plugins > Add New${NC}"
    echo -e "${GRAY}2. Click 'Upload Plugin'${NC}"
    echo -e "${GRAY}3. Choose the zip file: $ZIP_FILE${NC}"
    echo -e "${GRAY}4. Click 'Install Now' and then 'Activate'${NC}"
    
    echo ""
    echo -e "${CYAN}Package contents:${NC}"
    
    # List contents of zip file
    if command -v unzip >/dev/null 2>&1; then
        unzip -l "$ZIP_FILE" | tail -n +4 | head -n -2 | awk '{print "  " $4}' | grep -v "^  $" | while read -r line; do
            echo -e "${GRAY}$line${NC}"
        done
    else
        echo -e "${DARK_GRAY}  (Install unzip to see package contents)${NC}"
    fi
else
    echo -e "${RED}✗ Error: Zip file was not created${NC}"
    exit 1
fi

echo ""
echo -e "${GREEN}Build script completed!${NC}"
