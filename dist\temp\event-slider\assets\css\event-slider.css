/**
 * Event Slider Styles
 */

/* Container Styles */
.event-slider-container {
    position: relative;
    width: 100%;
    overflow: hidden;
    margin: 0 auto;
}

.event-slider-wrapper {
    display: flex;
    transition: transform 0.3s ease-in-out;
    width: 100%;
}

/* Slide Styles */
.event-slider-slide {
    flex: 0 0 auto;
    width: 100%;
    padding: 15px;
    box-sizing: border-box;
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin: 0 10px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.event-slider-slide:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
}

/* Image Styles */
.event-slide-image {
    width: 100%;
    height: 200px;
    overflow: hidden;
    border-radius: 6px;
    margin-bottom: 15px;
}

.event-slide-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.event-slider-slide:hover .event-slide-image img {
    transform: scale(1.05);
}

/* Content Styles */
.event-slide-content {
    padding: 0;
}

.event-slide-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #333333;
    margin: 0 0 10px 0;
    line-height: 1.4;
}

.event-slide-subtitle {
    font-size: 0.95rem;
    color: #666666;
    line-height: 1.5;
    margin: 0 0 15px 0;
}

/* Button Styles */
.event-slide-button {
    margin-top: 15px;
}

.event-slider-btn {
    display: inline-block;
    padding: 10px 20px;
    background-color: #007cba;
    color: #ffffff;
    text-decoration: none;
    border-radius: 4px;
    font-size: 0.9rem;
    font-weight: 500;
    transition: background-color 0.3s ease, transform 0.2s ease;
    border: none;
    cursor: pointer;
}

.event-slider-btn:hover {
    background-color: #005a87;
    transform: translateY(-1px);
    color: #ffffff;
    text-decoration: none;
}

/* Navigation Styles */
.event-slider-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 100%;
    display: flex;
    justify-content: space-between;
    pointer-events: none;
    z-index: 10;
}

.event-slider-prev,
.event-slider-next {
    background-color: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.3s ease, transform 0.2s ease;
    pointer-events: auto;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.event-slider-prev:hover,
.event-slider-next:hover {
    background-color: #ffffff;
    transform: scale(1.1);
}

.event-slider-prev {
    margin-left: -20px;
}

.event-slider-next {
    margin-right: -20px;
}

.event-slider-prev i,
.event-slider-next i {
    color: #333333;
    font-size: 14px;
}

/* Dots Styles */
.event-slider-dots {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 20px;
    gap: 8px;
}

.event-slider-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: none;
    background-color: #cccccc;
    cursor: pointer;
    transition: background-color 0.3s ease, transform 0.2s ease;
}

.event-slider-dot:hover {
    background-color: #999999;
    transform: scale(1.2);
}

.event-slider-dot.active {
    background-color: #007cba;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .event-slider-slide {
        padding: 12px;
        margin: 0 5px;
    }
    
    .event-slide-image {
        height: 150px;
        margin-bottom: 12px;
    }
    
    .event-slide-title {
        font-size: 1.1rem;
    }
    
    .event-slide-subtitle {
        font-size: 0.9rem;
    }
    
    .event-slider-btn {
        padding: 8px 16px;
        font-size: 0.85rem;
    }
    
    .event-slider-prev,
    .event-slider-next {
        width: 35px;
        height: 35px;
    }
    
    .event-slider-prev {
        margin-left: -17px;
    }
    
    .event-slider-next {
        margin-right: -17px;
    }
}

@media (max-width: 480px) {
    .event-slider-slide {
        padding: 10px;
        margin: 0 3px;
    }
    
    .event-slide-image {
        height: 120px;
        margin-bottom: 10px;
    }
    
    .event-slide-title {
        font-size: 1rem;
        margin-bottom: 8px;
    }
    
    .event-slide-subtitle {
        font-size: 0.85rem;
        margin-bottom: 12px;
    }
    
    .event-slider-btn {
        padding: 7px 14px;
        font-size: 0.8rem;
    }
    
    .event-slider-prev,
    .event-slider-next {
        width: 30px;
        height: 30px;
    }
    
    .event-slider-prev i,
    .event-slider-next i {
        font-size: 12px;
    }
    
    .event-slider-dot {
        width: 10px;
        height: 10px;
    }
}

/* Loading State */
.event-slider-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    color: #666666;
}

/* Empty State */
.event-slider-empty {
    text-align: center;
    padding: 40px 20px;
    color: #666666;
    font-style: italic;
}

/* Animation Classes */
.event-slider-fade-in {
    animation: eventSliderFadeIn 0.5s ease-in-out;
}

@keyframes eventSliderFadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Accessibility */
.event-slider-prev:focus,
.event-slider-next:focus,
.event-slider-dot:focus {
    outline: 2px solid #007cba;
    outline-offset: 2px;
}

/* RTL Support */
[dir="rtl"] .event-slider-wrapper {
    direction: rtl;
}

[dir="rtl"] .event-slider-prev {
    margin-left: 0;
    margin-right: -20px;
}

[dir="rtl"] .event-slider-next {
    margin-right: 0;
    margin-left: -20px;
}
