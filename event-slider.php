<?php
/**
 * Plugin Name: Event Slider
 * Plugin URI: https://yourwebsite.com/event-slider
 * Description: An Elementor add-on that creates a customizable event slider widget with images, titles, subtitles, and action buttons.
 * Version: 1.0.0
 * Author: Your Name
 * Author URI: https://yourwebsite.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: event-slider
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * Elementor tested up to: 3.18
 * Elementor Pro tested up to: 3.18
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('EVENT_SLIDER_VERSION', '1.0.0');
define('EVENT_SLIDER_PLUGIN_URL', plugin_dir_url(__FILE__));
define('EVENT_SLIDER_PLUGIN_PATH', plugin_dir_path(__FILE__));
define('EVENT_SLIDER_PLUGIN_FILE', __FILE__);

/**
 * Main Event Slider Plugin Class
 */
final class Event_Slider_Plugin {

    /**
     * Plugin instance
     */
    private static $_instance = null;

    /**
     * Minimum Elementor Version
     */
    const MINIMUM_ELEMENTOR_VERSION = '3.0.0';

    /**
     * Minimum PHP Version
     */
    const MINIMUM_PHP_VERSION = '7.4';

    /**
     * Instance
     */
    public static function instance() {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    /**
     * Constructor
     */
    public function __construct() {
        add_action('plugins_loaded', [$this, 'init']);
    }

    /**
     * Initialize the plugin
     */
    public function init() {
        // Check if Elementor is installed and activated
        if (!did_action('elementor/loaded')) {
            add_action('admin_notices', [$this, 'admin_notice_missing_main_plugin']);
            return;
        }

        // Check for required Elementor version
        if (!version_compare(ELEMENTOR_VERSION, self::MINIMUM_ELEMENTOR_VERSION, '>=')) {
            add_action('admin_notices', [$this, 'admin_notice_minimum_elementor_version']);
            return;
        }

        // Check for required PHP version
        if (version_compare(PHP_VERSION, self::MINIMUM_PHP_VERSION, '<')) {
            add_action('admin_notices', [$this, 'admin_notice_minimum_php_version']);
            return;
        }

        // Load plugin files
        $this->load_files();

        // Initialize plugin
        add_action('elementor/widgets/register', [$this, 'register_widgets']);
        add_action('elementor/frontend/after_enqueue_styles', [$this, 'enqueue_frontend_styles']);
        add_action('elementor/frontend/after_register_scripts', [$this, 'enqueue_frontend_scripts']);
        add_action('elementor/editor/before_enqueue_scripts', [$this, 'enqueue_editor_scripts']);

        // Load text domain
        add_action('init', [$this, 'load_textdomain']);
    }

    /**
     * Load plugin files
     */
    private function load_files() {
        require_once EVENT_SLIDER_PLUGIN_PATH . 'includes/class-event-slider.php';
        require_once EVENT_SLIDER_PLUGIN_PATH . 'includes/widgets/class-event-slider-widget.php';
    }

    /**
     * Register widgets
     */
    public function register_widgets($widgets_manager) {
        $widgets_manager->register(new \Event_Slider_Widget());
    }

    /**
     * Enqueue frontend styles
     */
    public function enqueue_frontend_styles() {
        wp_enqueue_style(
            'event-slider-frontend',
            EVENT_SLIDER_PLUGIN_URL . 'assets/css/event-slider.css',
            [],
            EVENT_SLIDER_VERSION
        );
    }

    /**
     * Enqueue frontend scripts
     */
    public function enqueue_frontend_scripts() {
        wp_enqueue_script(
            'event-slider-frontend',
            EVENT_SLIDER_PLUGIN_URL . 'assets/js/event-slider.js',
            ['jquery'],
            EVENT_SLIDER_VERSION,
            true
        );
    }

    /**
     * Enqueue editor scripts
     */
    public function enqueue_editor_scripts() {
        wp_enqueue_script(
            'event-slider-editor',
            EVENT_SLIDER_PLUGIN_URL . 'assets/js/event-slider-editor.js',
            ['elementor-editor'],
            EVENT_SLIDER_VERSION,
            true
        );
    }

    /**
     * Load text domain
     */
    public function load_textdomain() {
        load_plugin_textdomain('event-slider', false, dirname(plugin_basename(__FILE__)) . '/languages');
    }

    /**
     * Admin notice for missing Elementor
     */
    public function admin_notice_missing_main_plugin() {
        if (isset($_GET['activate'])) unset($_GET['activate']);

        $message = sprintf(
            esc_html__('"%1$s" requires "%2$s" to be installed and activated.', 'event-slider'),
            '<strong>' . esc_html__('Event Slider', 'event-slider') . '</strong>',
            '<strong>' . esc_html__('Elementor', 'event-slider') . '</strong>'
        );

        printf('<div class="notice notice-warning is-dismissible"><p>%1$s</p></div>', $message);
    }

    /**
     * Admin notice for minimum Elementor version
     */
    public function admin_notice_minimum_elementor_version() {
        if (isset($_GET['activate'])) unset($_GET['activate']);

        $message = sprintf(
            esc_html__('"%1$s" requires "%2$s" version %3$s or greater.', 'event-slider'),
            '<strong>' . esc_html__('Event Slider', 'event-slider') . '</strong>',
            '<strong>' . esc_html__('Elementor', 'event-slider') . '</strong>',
            self::MINIMUM_ELEMENTOR_VERSION
        );

        printf('<div class="notice notice-warning is-dismissible"><p>%1$s</p></div>', $message);
    }

    /**
     * Admin notice for minimum PHP version
     */
    public function admin_notice_minimum_php_version() {
        if (isset($_GET['activate'])) unset($_GET['activate']);

        $message = sprintf(
            esc_html__('"%1$s" requires "%2$s" version %3$s or greater.', 'event-slider'),
            '<strong>' . esc_html__('Event Slider', 'event-slider') . '</strong>',
            '<strong>' . esc_html__('PHP', 'event-slider') . '</strong>',
            self::MINIMUM_PHP_VERSION
        );

        printf('<div class="notice notice-warning is-dismissible"><p>%1$s</p></div>', $message);
    }
}

// Initialize the plugin
Event_Slider_Plugin::instance();

/**
 * Plugin activation hook
 */
register_activation_hook(__FILE__, function() {
    // Activation code here if needed
});

/**
 * Plugin deactivation hook
 */
register_deactivation_hook(__FILE__, function() {
    // Deactivation code here if needed
});
