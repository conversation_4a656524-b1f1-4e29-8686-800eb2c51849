<?php
/**
 * Plugin Name: Event Slider
 * Plugin URI: https://yourwebsite.com/event-slider
 * Description: An Elementor add-on that creates a customizable event slider widget with images, titles, subtitles, and action buttons.
 * Version: 1.0.0
 * Author: Your Name
 * Author URI: https://yourwebsite.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: event-slider
 * Domain Path: /languages
 * Requires at least: 6.8
 * Tested up to: 6.8
 * Requires PHP: 8.0
 * Elementor tested up to: 3.29
 * Elementor Pro tested up to: 3.29
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('EVENT_SLIDER_VERSION', '1.0.0');
define('EVENT_SLIDER_PLUGIN_URL', plugin_dir_url(__FILE__));
define('EVENT_SLIDER_PLUGIN_PATH', plugin_dir_path(__FILE__));
define('EVENT_SLIDER_PLUGIN_FILE', __FILE__);

/**
 * Main Event Slider Plugin Class
 */
final class Event_Slider_Plugin {

    /**
     * Plugin instance
     */
    private static $_instance = null;

    /**
     * Minimum Elementor Version
     */
    const MINIMUM_ELEMENTOR_VERSION = '3.29.0';

    /**
     * Minimum PHP Version
     */
    const MINIMUM_PHP_VERSION = '8.0';

    /**
     * Instance
     */
    public static function instance() {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    /**
     * Constructor
     */
    public function __construct() {
        add_action('plugins_loaded', array($this, 'init'));
    }

    /**
     * Initialize the plugin
     */
    public function init() {
        // Check if Elementor is installed and activated
        if (!did_action('elementor/loaded')) {
            add_action('admin_notices', array($this, 'admin_notice_missing_main_plugin'));
            return;
        }

        // Check for required Elementor version
        if (!defined('ELEMENTOR_VERSION') || !version_compare(ELEMENTOR_VERSION, self::MINIMUM_ELEMENTOR_VERSION, '>=')) {
            add_action('admin_notices', array($this, 'admin_notice_minimum_elementor_version'));
            return;
        }

        // Check for required PHP version
        if (version_compare(PHP_VERSION, self::MINIMUM_PHP_VERSION, '<')) {
            add_action('admin_notices', array($this, 'admin_notice_minimum_php_version'));
            return;
        }

        // Check WordPress version
        if (version_compare(get_bloginfo('version'), '6.8', '<')) {
            add_action('admin_notices', array($this, 'admin_notice_minimum_wordpress_version'));
            return;
        }

        // Load plugin files
        if (!$this->load_files()) {
            return;
        }

        // Initialize plugin
        add_action('elementor/widgets/register', array($this, 'register_widgets'));
        add_action('elementor/frontend/after_enqueue_styles', array($this, 'enqueue_frontend_styles'));
        add_action('elementor/frontend/after_register_scripts', array($this, 'enqueue_frontend_scripts'));
        add_action('elementor/editor/before_enqueue_scripts', array($this, 'enqueue_editor_scripts'));

        // Load text domain
        add_action('init', array($this, 'load_textdomain'));
    }

    /**
     * Load plugin files
     */
    private function load_files() {
        $files = array(
            EVENT_SLIDER_PLUGIN_PATH . 'includes/class-event-slider.php',
            EVENT_SLIDER_PLUGIN_PATH . 'includes/widgets/class-event-slider-widget.php'
        );

        foreach ($files as $file) {
            if (file_exists($file)) {
                require_once $file;
            } else {
                add_action('admin_notices', function() use ($file) {
                    $message = sprintf(
                        esc_html__('Event Slider: Required file missing: %s', 'event-slider'),
                        basename($file)
                    );
                    printf('<div class="notice notice-error is-dismissible"><p>%s</p></div>', $message);
                });
                return false;
            }
        }
        return true;
    }

    /**
     * Register widgets
     */
    public function register_widgets($widgets_manager) {
        // Check if required classes exist
        if (!class_exists('Event_Slider_Widget')) {
            return;
        }

        try {
            // Check if the newer register method exists (Elementor 3.5+)
            if (method_exists($widgets_manager, 'register')) {
                $widgets_manager->register(new Event_Slider_Widget());
            } else {
                // Fallback for older versions
                $widgets_manager->register_widget_type(new Event_Slider_Widget());
            }
        } catch (Exception $e) {
            // Log error if possible
            if (function_exists('error_log')) {
                error_log('Event Slider: Widget registration failed - ' . $e->getMessage());
            }
        }
    }

    /**
     * Enqueue frontend styles
     */
    public function enqueue_frontend_styles() {
        wp_enqueue_style(
            'event-slider-frontend',
            EVENT_SLIDER_PLUGIN_URL . 'assets/css/event-slider.css',
            array(),
            EVENT_SLIDER_VERSION
        );
    }

    /**
     * Enqueue frontend scripts
     */
    public function enqueue_frontend_scripts() {
        wp_enqueue_script(
            'event-slider-frontend',
            EVENT_SLIDER_PLUGIN_URL . 'assets/js/event-slider.js',
            array('jquery'),
            EVENT_SLIDER_VERSION,
            true
        );
    }

    /**
     * Enqueue editor scripts
     */
    public function enqueue_editor_scripts() {
        wp_enqueue_script(
            'event-slider-editor',
            EVENT_SLIDER_PLUGIN_URL . 'assets/js/event-slider-editor.js',
            array('elementor-editor'),
            EVENT_SLIDER_VERSION,
            true
        );
    }

    /**
     * Load text domain
     */
    public function load_textdomain() {
        load_plugin_textdomain('event-slider', false, dirname(plugin_basename(__FILE__)) . '/languages');
    }

    /**
     * Admin notice for missing Elementor
     */
    public function admin_notice_missing_main_plugin() {
        if (isset($_GET['activate'])) unset($_GET['activate']);

        $message = sprintf(
            esc_html__('"%1$s" requires "%2$s" to be installed and activated.', 'event-slider'),
            '<strong>' . esc_html__('Event Slider', 'event-slider') . '</strong>',
            '<strong>' . esc_html__('Elementor', 'event-slider') . '</strong>'
        );

        printf('<div class="notice notice-warning is-dismissible"><p>%1$s</p></div>', $message);
    }

    /**
     * Admin notice for minimum Elementor version
     */
    public function admin_notice_minimum_elementor_version() {
        if (isset($_GET['activate'])) unset($_GET['activate']);

        $message = sprintf(
            esc_html__('"%1$s" requires "%2$s" version %3$s or greater.', 'event-slider'),
            '<strong>' . esc_html__('Event Slider', 'event-slider') . '</strong>',
            '<strong>' . esc_html__('Elementor', 'event-slider') . '</strong>',
            self::MINIMUM_ELEMENTOR_VERSION
        );

        printf('<div class="notice notice-warning is-dismissible"><p>%1$s</p></div>', $message);
    }

    /**
     * Admin notice for minimum PHP version
     */
    public function admin_notice_minimum_php_version() {
        if (isset($_GET['activate'])) unset($_GET['activate']);

        $message = sprintf(
            esc_html__('"%1$s" requires "%2$s" version %3$s or greater.', 'event-slider'),
            '<strong>' . esc_html__('Event Slider', 'event-slider') . '</strong>',
            '<strong>' . esc_html__('PHP', 'event-slider') . '</strong>',
            self::MINIMUM_PHP_VERSION
        );

        printf('<div class="notice notice-warning is-dismissible"><p>%1$s</p></div>', $message);
    }

    /**
     * Admin notice for minimum WordPress version
     */
    public function admin_notice_minimum_wordpress_version() {
        if (isset($_GET['activate'])) unset($_GET['activate']);

        $message = sprintf(
            esc_html__('"%1$s" requires "%2$s" version %3$s or greater.', 'event-slider'),
            '<strong>' . esc_html__('Event Slider', 'event-slider') . '</strong>',
            '<strong>' . esc_html__('WordPress', 'event-slider') . '</strong>',
            '6.8'
        );

        printf('<div class="notice notice-warning is-dismissible"><p>%1$s</p></div>', $message);
    }
}

// Initialize the plugin
Event_Slider_Plugin::instance();

/**
 * Plugin activation hook
 */
register_activation_hook(__FILE__, function() {
    // Activation code here if needed
});

/**
 * Plugin deactivation hook
 */
register_deactivation_hook(__FILE__, function() {
    // Deactivation code here if needed
});
