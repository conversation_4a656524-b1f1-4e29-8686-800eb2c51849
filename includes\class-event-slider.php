<?php
/**
 * Event Slider Main Class
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Main Event Slider Class
 */
class Event_Slider {

    /**
     * Instance
     */
    private static $_instance = null;

    /**
     * Get instance
     */
    public static function instance() {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    /**
     * Constructor
     */
    public function __construct() {
        $this->init_hooks();
    }

    /**
     * Initialize hooks
     */
    private function init_hooks() {
        add_action('elementor/elements/categories_registered', [$this, 'add_elementor_widget_categories']);
    }

    /**
     * Add custom Elementor widget category
     */
    public function add_elementor_widget_categories($elements_manager) {
        $elements_manager->add_category(
            'event-slider-category',
            [
                'title' => esc_html__('Event Slider', 'event-slider'),
                'icon' => 'fa fa-calendar',
            ]
        );
    }

    /**
     * Get default slider settings
     */
    public static function get_default_settings() {
        return [
            'autoplay' => true,
            'autoplay_speed' => 3000,
            'infinite' => true,
            'show_arrows' => true,
            'show_dots' => true,
            'slides_to_show' => 3,
            'slides_to_scroll' => 1,
            'responsive_breakpoints' => [
                'tablet' => 768,
                'mobile' => 480
            ]
        ];
    }

    /**
     * Sanitize slider settings
     */
    public static function sanitize_settings($settings) {
        $sanitized = [];
        
        $sanitized['autoplay'] = isset($settings['autoplay']) ? (bool) $settings['autoplay'] : true;
        $sanitized['autoplay_speed'] = isset($settings['autoplay_speed']) ? absint($settings['autoplay_speed']) : 3000;
        $sanitized['infinite'] = isset($settings['infinite']) ? (bool) $settings['infinite'] : true;
        $sanitized['show_arrows'] = isset($settings['show_arrows']) ? (bool) $settings['show_arrows'] : true;
        $sanitized['show_dots'] = isset($settings['show_dots']) ? (bool) $settings['show_dots'] : true;
        $sanitized['slides_to_show'] = isset($settings['slides_to_show']) ? absint($settings['slides_to_show']) : 3;
        $sanitized['slides_to_scroll'] = isset($settings['slides_to_scroll']) ? absint($settings['slides_to_scroll']) : 1;

        return $sanitized;
    }

    /**
     * Generate unique slider ID
     */
    public static function generate_slider_id() {
        return 'event-slider-' . uniqid();
    }

    /**
     * Get slider HTML structure
     */
    public static function get_slider_html($slides, $settings, $slider_id) {
        if (empty($slides)) {
            return '<div class="event-slider-empty">' . esc_html__('No events to display.', 'event-slider') . '</div>';
        }

        $html = '<div class="event-slider-container" id="' . esc_attr($slider_id) . '">';
        $html .= '<div class="event-slider-wrapper">';

        foreach ($slides as $slide) {
            $html .= self::get_slide_html($slide);
        }

        $html .= '</div>';

        // Add navigation if enabled
        if ($settings['show_arrows']) {
            $html .= '<div class="event-slider-nav">';
            $html .= '<button class="event-slider-prev" aria-label="' . esc_attr__('Previous slide', 'event-slider') . '">';
            $html .= '<i class="fas fa-chevron-left"></i>';
            $html .= '</button>';
            $html .= '<button class="event-slider-next" aria-label="' . esc_attr__('Next slide', 'event-slider') . '">';
            $html .= '<i class="fas fa-chevron-right"></i>';
            $html .= '</button>';
            $html .= '</div>';
        }

        // Add dots if enabled
        if ($settings['show_dots']) {
            $html .= '<div class="event-slider-dots">';
            for ($i = 0; $i < count($slides); $i++) {
                $active_class = $i === 0 ? ' active' : '';
                $html .= '<button class="event-slider-dot' . $active_class . '" data-slide="' . $i . '" aria-label="' . sprintf(esc_attr__('Go to slide %d', 'event-slider'), $i + 1) . '"></button>';
            }
            $html .= '</div>';
        }

        $html .= '</div>';

        return $html;
    }

    /**
     * Get individual slide HTML
     */
    private static function get_slide_html($slide) {
        $html = '<div class="event-slider-slide">';
        
        // Image
        if (!empty($slide['image']['url'])) {
            $html .= '<div class="event-slide-image">';
            $html .= '<img src="' . esc_url($slide['image']['url']) . '" alt="' . esc_attr($slide['title']) . '">';
            $html .= '</div>';
        }

        // Content
        $html .= '<div class="event-slide-content">';
        
        // Title
        if (!empty($slide['title'])) {
            $html .= '<h3 class="event-slide-title">' . esc_html($slide['title']) . '</h3>';
        }

        // Subtitle
        if (!empty($slide['subtitle'])) {
            $html .= '<p class="event-slide-subtitle">' . esc_html($slide['subtitle']) . '</p>';
        }

        // Button
        if (!empty($slide['button_text']) && !empty($slide['button_link']['url'])) {
            $target = $slide['button_link']['is_external'] ? ' target="_blank"' : '';
            $nofollow = $slide['button_link']['nofollow'] ? ' rel="nofollow"' : '';
            
            $html .= '<div class="event-slide-button">';
            $html .= '<a href="' . esc_url($slide['button_link']['url']) . '"' . $target . $nofollow . ' class="event-slider-btn">';
            $html .= esc_html($slide['button_text']);
            $html .= '</a>';
            $html .= '</div>';
        }

        $html .= '</div>';
        $html .= '</div>';

        return $html;
    }

    /**
     * Generate slider JavaScript configuration
     */
    public static function get_slider_config($settings, $slider_id) {
        $config = [
            'autoplay' => $settings['autoplay'],
            'autoplaySpeed' => $settings['autoplay_speed'],
            'infinite' => $settings['infinite'],
            'arrows' => $settings['show_arrows'],
            'dots' => $settings['show_dots'],
            'slidesToShow' => $settings['slides_to_show'],
            'slidesToScroll' => $settings['slides_to_scroll'],
            'responsive' => [
                [
                    'breakpoint' => 768,
                    'settings' => [
                        'slidesToShow' => min(2, $settings['slides_to_show']),
                        'slidesToScroll' => 1
                    ]
                ],
                [
                    'breakpoint' => 480,
                    'settings' => [
                        'slidesToShow' => 1,
                        'slidesToScroll' => 1
                    ]
                ]
            ]
        ];

        return $config;
    }
}
