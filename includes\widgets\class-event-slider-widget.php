<?php
/**
 * Event Slider Elementor Widget (Fixed Version)
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

use Elementor\Widget_Base;
use Elementor\Controls_Manager;
use Elementor\Repeater;
use Elementor\Utils;
use Elementor\Group_Control_Typography;
use Elementor\Group_Control_Background;
use Elementor\Group_Control_Border;
use Elementor\Group_Control_Box_Shadow;

/**
 * Event Slider Widget Class
 */
class Event_Slider_Widget extends Widget_Base {

    /**
     * Get widget name
     */
    public function get_name() {
        return 'event-slider';
    }

    /**
     * Get widget title
     */
    public function get_title() {
        return esc_html__('Event Slider', 'event-slider');
    }

    /**
     * Get widget icon
     */
    public function get_icon() {
        return 'eicon-slider-push';
    }

    /**
     * Get widget categories
     */
    public function get_categories() {
        return array('event-slider-category');
    }

    /**
     * Get widget keywords
     */
    public function get_keywords() {
        return array('event', 'slider', 'carousel', 'events');
    }

    /**
     * Register widget controls
     */
    protected function register_controls() {
        $this->start_controls_section(
            'content_section',
            array(
                'label' => esc_html__('Events', 'event-slider'),
                'tab' => Controls_Manager::TAB_CONTENT,
            )
        );

        $repeater = new Repeater();

        $repeater->add_control(
            'image',
            array(
                'label' => esc_html__('Event Image', 'event-slider'),
                'type' => Controls_Manager::MEDIA,
                'default' => array(
                    'url' => Utils::get_placeholder_image_src(),
                ),
            )
        );

        $repeater->add_control(
            'title',
            array(
                'label' => esc_html__('Event Title', 'event-slider'),
                'type' => Controls_Manager::TEXT,
                'default' => esc_html__('Event Title', 'event-slider'),
                'placeholder' => esc_html__('Enter event title', 'event-slider'),
                'label_block' => true,
            )
        );

        $repeater->add_control(
            'subtitle',
            array(
                'label' => esc_html__('Event Subtitle', 'event-slider'),
                'type' => Controls_Manager::TEXTAREA,
                'default' => esc_html__('Event description or subtitle goes here.', 'event-slider'),
                'placeholder' => esc_html__('Enter event subtitle or description', 'event-slider'),
                'rows' => 3,
            )
        );

        $repeater->add_control(
            'button_text',
            array(
                'label' => esc_html__('Button Text', 'event-slider'),
                'type' => Controls_Manager::TEXT,
                'default' => esc_html__('Learn More', 'event-slider'),
                'placeholder' => esc_html__('Enter button text', 'event-slider'),
            )
        );

        $repeater->add_control(
            'button_link',
            array(
                'label' => esc_html__('Button Link', 'event-slider'),
                'type' => Controls_Manager::URL,
                'placeholder' => esc_html__('https://your-link.com', 'event-slider'),
                'default' => array(
                    'url' => '#',
                ),
            )
        );

        $this->add_control(
            'slides',
            array(
                'label' => esc_html__('Event Slides', 'event-slider'),
                'type' => Controls_Manager::REPEATER,
                'fields' => $repeater->get_controls(),
                'default' => array(
                    array(
                        'title' => esc_html__('Event #1', 'event-slider'),
                        'subtitle' => esc_html__('This is the first event description.', 'event-slider'),
                        'button_text' => esc_html__('Learn More', 'event-slider'),
                        'button_link' => array('url' => '#'),
                    ),
                    array(
                        'title' => esc_html__('Event #2', 'event-slider'),
                        'subtitle' => esc_html__('This is the second event description.', 'event-slider'),
                        'button_text' => esc_html__('Learn More', 'event-slider'),
                        'button_link' => array('url' => '#'),
                    ),
                    array(
                        'title' => esc_html__('Event #3', 'event-slider'),
                        'subtitle' => esc_html__('This is the third event description.', 'event-slider'),
                        'button_text' => esc_html__('Learn More', 'event-slider'),
                        'button_link' => array('url' => '#'),
                    ),
                ),
                'title_field' => '{{{ title }}}',
            )
        );

        $this->end_controls_section();

        // Slider Settings Section
        $this->start_controls_section(
            'slider_settings_section',
            array(
                'label' => esc_html__('Slider Settings', 'event-slider'),
                'tab' => Controls_Manager::TAB_CONTENT,
            )
        );

        $this->add_control(
            'slides_to_show',
            array(
                'label' => esc_html__('Slides to Show', 'event-slider'),
                'type' => Controls_Manager::NUMBER,
                'min' => 1,
                'max' => 6,
                'step' => 1,
                'default' => 3,
            )
        );

        $this->add_control(
            'slides_to_scroll',
            array(
                'label' => esc_html__('Slides to Scroll', 'event-slider'),
                'type' => Controls_Manager::NUMBER,
                'min' => 1,
                'max' => 6,
                'step' => 1,
                'default' => 1,
            )
        );

        $this->add_control(
            'autoplay',
            array(
                'label' => esc_html__('Autoplay', 'event-slider'),
                'type' => Controls_Manager::SWITCHER,
                'label_on' => esc_html__('Yes', 'event-slider'),
                'label_off' => esc_html__('No', 'event-slider'),
                'return_value' => 'yes',
                'default' => 'yes',
            )
        );

        $this->add_control(
            'autoplay_speed',
            array(
                'label' => esc_html__('Autoplay Speed (ms)', 'event-slider'),
                'type' => Controls_Manager::NUMBER,
                'min' => 1000,
                'max' => 10000,
                'step' => 100,
                'default' => 3000,
                'condition' => array(
                    'autoplay' => 'yes',
                ),
            )
        );

        $this->add_control(
            'infinite',
            array(
                'label' => esc_html__('Infinite Loop', 'event-slider'),
                'type' => Controls_Manager::SWITCHER,
                'label_on' => esc_html__('Yes', 'event-slider'),
                'label_off' => esc_html__('No', 'event-slider'),
                'return_value' => 'yes',
                'default' => 'yes',
            )
        );

        $this->add_control(
            'show_arrows',
            array(
                'label' => esc_html__('Show Arrows', 'event-slider'),
                'type' => Controls_Manager::SWITCHER,
                'label_on' => esc_html__('Yes', 'event-slider'),
                'label_off' => esc_html__('No', 'event-slider'),
                'return_value' => 'yes',
                'default' => 'yes',
            )
        );

        $this->add_control(
            'show_dots',
            array(
                'label' => esc_html__('Show Dots', 'event-slider'),
                'type' => Controls_Manager::SWITCHER,
                'label_on' => esc_html__('Yes', 'event-slider'),
                'label_off' => esc_html__('No', 'event-slider'),
                'return_value' => 'yes',
                'default' => 'yes',
            )
        );

        $this->end_controls_section();

        // Basic style controls
        $this->register_basic_style_controls();
    }

    /**
     * Register basic style controls
     */
    private function register_basic_style_controls() {
        // Slide Style Section
        $this->start_controls_section(
            'slide_style_section',
            array(
                'label' => esc_html__('Slide Style', 'event-slider'),
                'tab' => Controls_Manager::TAB_STYLE,
            )
        );

        $this->add_responsive_control(
            'slide_padding',
            array(
                'label' => esc_html__('Padding', 'event-slider'),
                'type' => Controls_Manager::DIMENSIONS,
                'size_units' => array('px', '%', 'em'),
                'selectors' => array(
                    '{{WRAPPER}} .event-slider-slide' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ),
            )
        );

        $this->end_controls_section();
    }

    /**
     * Render widget output on the frontend
     */
    protected function render() {
        $settings = $this->get_settings_for_display();
        $slides = $settings['slides'];

        if (empty($slides)) {
            return;
        }

        $slider_id = 'event-slider-' . uniqid();
        
        // Prepare slider settings
        $slider_settings = array(
            'autoplay' => $settings['autoplay'] === 'yes',
            'autoplay_speed' => $settings['autoplay_speed'],
            'infinite' => $settings['infinite'] === 'yes',
            'show_arrows' => $settings['show_arrows'] === 'yes',
            'show_dots' => $settings['show_dots'] === 'yes',
            'slides_to_show' => $settings['slides_to_show'],
            'slides_to_scroll' => $settings['slides_to_scroll'],
        );

        // Output the slider
        echo $this->get_slider_html($slides, $slider_settings, $slider_id);
    }

    /**
     * Get slider HTML
     */
    private function get_slider_html($slides, $settings, $slider_id) {
        if (empty($slides)) {
            return '<div class="event-slider-empty">' . esc_html__('No events to display.', 'event-slider') . '</div>';
        }

        $html = '<div class="event-slider-container" id="' . esc_attr($slider_id) . '">';
        $html .= '<div class="event-slider-wrapper">';

        foreach ($slides as $slide) {
            $html .= $this->get_slide_html($slide);
        }

        $html .= '</div>';
        $html .= '</div>';

        return $html;
    }

    /**
     * Get individual slide HTML
     */
    private function get_slide_html($slide) {
        $html = '<div class="event-slider-slide">';
        
        // Image
        if (!empty($slide['image']['url'])) {
            $html .= '<div class="event-slide-image">';
            $html .= '<img src="' . esc_url($slide['image']['url']) . '" alt="' . esc_attr($slide['title']) . '">';
            $html .= '</div>';
        }

        // Content
        $html .= '<div class="event-slide-content">';
        
        // Title
        if (!empty($slide['title'])) {
            $html .= '<h3 class="event-slide-title">' . esc_html($slide['title']) . '</h3>';
        }

        // Subtitle
        if (!empty($slide['subtitle'])) {
            $html .= '<p class="event-slide-subtitle">' . esc_html($slide['subtitle']) . '</p>';
        }

        // Button
        if (!empty($slide['button_text']) && !empty($slide['button_link']['url'])) {
            $target = !empty($slide['button_link']['is_external']) ? ' target="_blank"' : '';
            $nofollow = !empty($slide['button_link']['nofollow']) ? ' rel="nofollow"' : '';
            
            $html .= '<div class="event-slide-button">';
            $html .= '<a href="' . esc_url($slide['button_link']['url']) . '"' . $target . $nofollow . ' class="event-slider-btn">';
            $html .= esc_html($slide['button_text']);
            $html .= '</a>';
            $html .= '</div>';
        }

        $html .= '</div>';
        $html .= '</div>';

        return $html;
    }
}
