<?php
/**
 * Event Slider Elementor Widget
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

use Elementor\Widget_Base;
use Elementor\Controls_Manager;
use Elementor\Repeater;
use Elementor\Utils;
use Elementor\Group_Control_Typography;
use Elementor\Group_Control_Background;
use Elementor\Group_Control_Border;
use Elementor\Group_Control_Box_Shadow;

/**
 * Event Slider Widget Class
 */
class Event_Slider_Widget extends Widget_Base {

    /**
     * Get widget name
     */
    public function get_name() {
        return 'event-slider';
    }

    /**
     * Get widget title
     */
    public function get_title() {
        return esc_html__('Event Slider', 'event-slider');
    }

    /**
     * Get widget icon
     */
    public function get_icon() {
        return 'eicon-slider-push';
    }

    /**
     * Get widget categories
     */
    public function get_categories() {
        return ['event-slider-category'];
    }

    /**
     * Get widget keywords
     */
    public function get_keywords() {
        return ['event', 'slider', 'carousel', 'events'];
    }

    /**
     * Register widget controls
     */
    protected function register_controls() {
        $this->start_controls_section(
            'content_section',
            [
                'label' => esc_html__('Events', 'event-slider'),
                'tab' => Controls_Manager::TAB_CONTENT,
            ]
        );

        $repeater = new Repeater();

        $repeater->add_control(
            'image',
            [
                'label' => esc_html__('Event Image', 'event-slider'),
                'type' => Controls_Manager::MEDIA,
                'default' => [
                    'url' => Utils::get_placeholder_image_src(),
                ],
            ]
        );

        $repeater->add_control(
            'title',
            [
                'label' => esc_html__('Event Title', 'event-slider'),
                'type' => Controls_Manager::TEXT,
                'default' => esc_html__('Event Title', 'event-slider'),
                'placeholder' => esc_html__('Enter event title', 'event-slider'),
                'label_block' => true,
            ]
        );

        $repeater->add_control(
            'subtitle',
            [
                'label' => esc_html__('Event Subtitle', 'event-slider'),
                'type' => Controls_Manager::TEXTAREA,
                'default' => esc_html__('Event description or subtitle goes here.', 'event-slider'),
                'placeholder' => esc_html__('Enter event subtitle or description', 'event-slider'),
                'rows' => 3,
            ]
        );

        $repeater->add_control(
            'button_text',
            [
                'label' => esc_html__('Button Text', 'event-slider'),
                'type' => Controls_Manager::TEXT,
                'default' => esc_html__('Learn More', 'event-slider'),
                'placeholder' => esc_html__('Enter button text', 'event-slider'),
            ]
        );

        $repeater->add_control(
            'button_link',
            [
                'label' => esc_html__('Button Link', 'event-slider'),
                'type' => Controls_Manager::URL,
                'placeholder' => esc_html__('https://your-link.com', 'event-slider'),
                'default' => [
                    'url' => '#',
                ],
            ]
        );

        $this->add_control(
            'slides',
            [
                'label' => esc_html__('Event Slides', 'event-slider'),
                'type' => Controls_Manager::REPEATER,
                'fields' => $repeater->get_controls(),
                'default' => [
                    [
                        'title' => esc_html__('Event #1', 'event-slider'),
                        'subtitle' => esc_html__('This is the first event description.', 'event-slider'),
                        'button_text' => esc_html__('Learn More', 'event-slider'),
                        'button_link' => ['url' => '#'],
                    ],
                    [
                        'title' => esc_html__('Event #2', 'event-slider'),
                        'subtitle' => esc_html__('This is the second event description.', 'event-slider'),
                        'button_text' => esc_html__('Learn More', 'event-slider'),
                        'button_link' => ['url' => '#'],
                    ],
                    [
                        'title' => esc_html__('Event #3', 'event-slider'),
                        'subtitle' => esc_html__('This is the third event description.', 'event-slider'),
                        'button_text' => esc_html__('Learn More', 'event-slider'),
                        'button_link' => ['url' => '#'],
                    ],
                ],
                'title_field' => '{{{ title }}}',
            ]
        );

        $this->end_controls_section();

        // Slider Settings Section
        $this->start_controls_section(
            'slider_settings_section',
            [
                'label' => esc_html__('Slider Settings', 'event-slider'),
                'tab' => Controls_Manager::TAB_CONTENT,
            ]
        );

        $this->add_control(
            'slides_to_show',
            [
                'label' => esc_html__('Slides to Show', 'event-slider'),
                'type' => Controls_Manager::NUMBER,
                'min' => 1,
                'max' => 6,
                'step' => 1,
                'default' => 3,
            ]
        );

        $this->add_control(
            'slides_to_scroll',
            [
                'label' => esc_html__('Slides to Scroll', 'event-slider'),
                'type' => Controls_Manager::NUMBER,
                'min' => 1,
                'max' => 6,
                'step' => 1,
                'default' => 1,
            ]
        );

        $this->add_control(
            'autoplay',
            [
                'label' => esc_html__('Autoplay', 'event-slider'),
                'type' => Controls_Manager::SWITCHER,
                'label_on' => esc_html__('Yes', 'event-slider'),
                'label_off' => esc_html__('No', 'event-slider'),
                'return_value' => 'yes',
                'default' => 'yes',
            ]
        );

        $this->add_control(
            'autoplay_speed',
            [
                'label' => esc_html__('Autoplay Speed (ms)', 'event-slider'),
                'type' => Controls_Manager::NUMBER,
                'min' => 1000,
                'max' => 10000,
                'step' => 100,
                'default' => 3000,
                'condition' => [
                    'autoplay' => 'yes',
                ],
            ]
        );

        $this->add_control(
            'infinite',
            [
                'label' => esc_html__('Infinite Loop', 'event-slider'),
                'type' => Controls_Manager::SWITCHER,
                'label_on' => esc_html__('Yes', 'event-slider'),
                'label_off' => esc_html__('No', 'event-slider'),
                'return_value' => 'yes',
                'default' => 'yes',
            ]
        );

        $this->add_control(
            'show_arrows',
            [
                'label' => esc_html__('Show Arrows', 'event-slider'),
                'type' => Controls_Manager::SWITCHER,
                'label_on' => esc_html__('Yes', 'event-slider'),
                'label_off' => esc_html__('No', 'event-slider'),
                'return_value' => 'yes',
                'default' => 'yes',
            ]
        );

        $this->add_control(
            'show_dots',
            [
                'label' => esc_html__('Show Dots', 'event-slider'),
                'type' => Controls_Manager::SWITCHER,
                'label_on' => esc_html__('Yes', 'event-slider'),
                'label_off' => esc_html__('No', 'event-slider'),
                'return_value' => 'yes',
                'default' => 'yes',
            ]
        );

        $this->end_controls_section();

        // Style sections will be added in the next part
        $this->register_style_controls();
    }

    /**
     * Register style controls
     */
    private function register_style_controls() {
        // Slide Style Section
        $this->start_controls_section(
            'slide_style_section',
            [
                'label' => esc_html__('Slide Style', 'event-slider'),
                'tab' => Controls_Manager::TAB_STYLE,
            ]
        );

        $this->add_responsive_control(
            'slide_padding',
            [
                'label' => esc_html__('Padding', 'event-slider'),
                'type' => Controls_Manager::DIMENSIONS,
                'size_units' => ['px', '%', 'em'],
                'selectors' => [
                    '{{WRAPPER}} .event-slider-slide' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->add_group_control(
            Group_Control_Background::get_type(),
            [
                'name' => 'slide_background',
                'label' => esc_html__('Background', 'event-slider'),
                'types' => ['classic', 'gradient'],
                'selector' => '{{WRAPPER}} .event-slider-slide',
            ]
        );

        $this->add_group_control(
            Group_Control_Border::get_type(),
            [
                'name' => 'slide_border',
                'label' => esc_html__('Border', 'event-slider'),
                'selector' => '{{WRAPPER}} .event-slider-slide',
            ]
        );

        $this->add_responsive_control(
            'slide_border_radius',
            [
                'label' => esc_html__('Border Radius', 'event-slider'),
                'type' => Controls_Manager::DIMENSIONS,
                'size_units' => ['px', '%'],
                'selectors' => [
                    '{{WRAPPER}} .event-slider-slide' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->add_group_control(
            Group_Control_Box_Shadow::get_type(),
            [
                'name' => 'slide_box_shadow',
                'label' => esc_html__('Box Shadow', 'event-slider'),
                'selector' => '{{WRAPPER}} .event-slider-slide',
            ]
        );

        $this->end_controls_section();

        // Title Style Section
        $this->start_controls_section(
            'title_style_section',
            [
                'label' => esc_html__('Title Style', 'event-slider'),
                'tab' => Controls_Manager::TAB_STYLE,
            ]
        );

        $this->add_group_control(
            Group_Control_Typography::get_type(),
            [
                'name' => 'title_typography',
                'label' => esc_html__('Typography', 'event-slider'),
                'selector' => '{{WRAPPER}} .event-slide-title',
            ]
        );

        $this->add_control(
            'title_color',
            [
                'label' => esc_html__('Color', 'event-slider'),
                'type' => Controls_Manager::COLOR,
                'selectors' => [
                    '{{WRAPPER}} .event-slide-title' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->add_responsive_control(
            'title_margin',
            [
                'label' => esc_html__('Margin', 'event-slider'),
                'type' => Controls_Manager::DIMENSIONS,
                'size_units' => ['px', '%', 'em'],
                'selectors' => [
                    '{{WRAPPER}} .event-slide-title' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->end_controls_section();

        // Subtitle Style Section
        $this->start_controls_section(
            'subtitle_style_section',
            [
                'label' => esc_html__('Subtitle Style', 'event-slider'),
                'tab' => Controls_Manager::TAB_STYLE,
            ]
        );

        $this->add_group_control(
            Group_Control_Typography::get_type(),
            [
                'name' => 'subtitle_typography',
                'label' => esc_html__('Typography', 'event-slider'),
                'selector' => '{{WRAPPER}} .event-slide-subtitle',
            ]
        );

        $this->add_control(
            'subtitle_color',
            [
                'label' => esc_html__('Color', 'event-slider'),
                'type' => Controls_Manager::COLOR,
                'selectors' => [
                    '{{WRAPPER}} .event-slide-subtitle' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->add_responsive_control(
            'subtitle_margin',
            [
                'label' => esc_html__('Margin', 'event-slider'),
                'type' => Controls_Manager::DIMENSIONS,
                'size_units' => ['px', '%', 'em'],
                'selectors' => [
                    '{{WRAPPER}} .event-slide-subtitle' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->end_controls_section();

        // Button Style Section
        $this->start_controls_section(
            'button_style_section',
            [
                'label' => esc_html__('Button Style', 'event-slider'),
                'tab' => Controls_Manager::TAB_STYLE,
            ]
        );

        $this->add_group_control(
            Group_Control_Typography::get_type(),
            [
                'name' => 'button_typography',
                'label' => esc_html__('Typography', 'event-slider'),
                'selector' => '{{WRAPPER}} .event-slider-btn',
            ]
        );

        $this->start_controls_tabs('button_style_tabs');

        $this->start_controls_tab(
            'button_normal_tab',
            [
                'label' => esc_html__('Normal', 'event-slider'),
            ]
        );

        $this->add_control(
            'button_color',
            [
                'label' => esc_html__('Text Color', 'event-slider'),
                'type' => Controls_Manager::COLOR,
                'selectors' => [
                    '{{WRAPPER}} .event-slider-btn' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->add_group_control(
            Group_Control_Background::get_type(),
            [
                'name' => 'button_background',
                'label' => esc_html__('Background', 'event-slider'),
                'types' => ['classic', 'gradient'],
                'selector' => '{{WRAPPER}} .event-slider-btn',
            ]
        );

        $this->end_controls_tab();

        $this->start_controls_tab(
            'button_hover_tab',
            [
                'label' => esc_html__('Hover', 'event-slider'),
            ]
        );

        $this->add_control(
            'button_hover_color',
            [
                'label' => esc_html__('Text Color', 'event-slider'),
                'type' => Controls_Manager::COLOR,
                'selectors' => [
                    '{{WRAPPER}} .event-slider-btn:hover' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->add_group_control(
            Group_Control_Background::get_type(),
            [
                'name' => 'button_hover_background',
                'label' => esc_html__('Background', 'event-slider'),
                'types' => ['classic', 'gradient'],
                'selector' => '{{WRAPPER}} .event-slider-btn:hover',
            ]
        );

        $this->end_controls_tab();

        $this->end_controls_tabs();

        $this->add_responsive_control(
            'button_padding',
            [
                'label' => esc_html__('Padding', 'event-slider'),
                'type' => Controls_Manager::DIMENSIONS,
                'size_units' => ['px', '%', 'em'],
                'selectors' => [
                    '{{WRAPPER}} .event-slider-btn' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
                'separator' => 'before',
            ]
        );

        $this->add_group_control(
            Group_Control_Border::get_type(),
            [
                'name' => 'button_border',
                'label' => esc_html__('Border', 'event-slider'),
                'selector' => '{{WRAPPER}} .event-slider-btn',
            ]
        );

        $this->add_responsive_control(
            'button_border_radius',
            [
                'label' => esc_html__('Border Radius', 'event-slider'),
                'type' => Controls_Manager::DIMENSIONS,
                'size_units' => ['px', '%'],
                'selectors' => [
                    '{{WRAPPER}} .event-slider-btn' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->end_controls_section();

        // Navigation Style Section
        $this->start_controls_section(
            'navigation_style_section',
            [
                'label' => esc_html__('Navigation Style', 'event-slider'),
                'tab' => Controls_Manager::TAB_STYLE,
            ]
        );

        $this->add_control(
            'arrows_heading',
            [
                'label' => esc_html__('Arrows', 'event-slider'),
                'type' => Controls_Manager::HEADING,
            ]
        );

        $this->add_control(
            'arrows_color',
            [
                'label' => esc_html__('Color', 'event-slider'),
                'type' => Controls_Manager::COLOR,
                'selectors' => [
                    '{{WRAPPER}} .event-slider-prev i, {{WRAPPER}} .event-slider-next i' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'arrows_background',
            [
                'label' => esc_html__('Background Color', 'event-slider'),
                'type' => Controls_Manager::COLOR,
                'selectors' => [
                    '{{WRAPPER}} .event-slider-prev, {{WRAPPER}} .event-slider-next' => 'background-color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'dots_heading',
            [
                'label' => esc_html__('Dots', 'event-slider'),
                'type' => Controls_Manager::HEADING,
                'separator' => 'before',
            ]
        );

        $this->add_control(
            'dots_color',
            [
                'label' => esc_html__('Color', 'event-slider'),
                'type' => Controls_Manager::COLOR,
                'selectors' => [
                    '{{WRAPPER}} .event-slider-dot' => 'background-color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'dots_active_color',
            [
                'label' => esc_html__('Active Color', 'event-slider'),
                'type' => Controls_Manager::COLOR,
                'selectors' => [
                    '{{WRAPPER}} .event-slider-dot.active' => 'background-color: {{VALUE}};',
                ],
            ]
        );

        $this->end_controls_section();
    }

    /**
     * Render widget output on the frontend
     */
    protected function render() {
        $settings = $this->get_settings_for_display();
        $slides = $settings['slides'];

        if (empty($slides)) {
            return;
        }

        $slider_id = Event_Slider::generate_slider_id();

        // Prepare slider settings
        $slider_settings = [
            'autoplay' => $settings['autoplay'] === 'yes',
            'autoplay_speed' => $settings['autoplay_speed'],
            'infinite' => $settings['infinite'] === 'yes',
            'show_arrows' => $settings['show_arrows'] === 'yes',
            'show_dots' => $settings['show_dots'] === 'yes',
            'slides_to_show' => $settings['slides_to_show'],
            'slides_to_scroll' => $settings['slides_to_scroll'],
        ];

        // Output the slider
        echo Event_Slider::get_slider_html($slides, $slider_settings, $slider_id);

        // Add inline script for this specific slider
        $config = Event_Slider::get_slider_config($slider_settings, $slider_id);
        ?>
        <script type="text/javascript">
            jQuery(document).ready(function($) {
                $('#<?php echo esc_js($slider_id); ?> .event-slider-wrapper').eventSlider(<?php echo wp_json_encode($config); ?>);
            });
        </script>
        <?php
    }

    /**
     * Render widget output in the editor
     */
    protected function content_template() {
        ?>
        <# if ( settings.slides.length ) { #>
            <div class="event-slider-container">
                <div class="event-slider-wrapper">
                    <# _.each( settings.slides, function( slide ) { #>
                        <div class="event-slider-slide">
                            <# if ( slide.image.url ) { #>
                                <div class="event-slide-image">
                                    <img src="{{ slide.image.url }}" alt="{{ slide.title }}">
                                </div>
                            <# } #>
                            <div class="event-slide-content">
                                <# if ( slide.title ) { #>
                                    <h3 class="event-slide-title">{{{ slide.title }}}</h3>
                                <# } #>
                                <# if ( slide.subtitle ) { #>
                                    <p class="event-slide-subtitle">{{{ slide.subtitle }}}</p>
                                <# } #>
                                <# if ( slide.button_text && slide.button_link.url ) { #>
                                    <div class="event-slide-button">
                                        <a href="{{ slide.button_link.url }}" class="event-slider-btn">{{{ slide.button_text }}}</a>
                                    </div>
                                <# } #>
                            </div>
                        </div>
                    <# }); #>
                </div>
            </div>
        <# } #>
        <?php
    }
}
